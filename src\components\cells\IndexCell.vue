<template>
  <div class="grid-cell index-cell">
    <div class="cell-content">
      {{ rowIndex + 1 }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'IndexCell',
  props: {
    rowIndex: {
      type: Number,
      required: true
    }
  }
}
</script>

<style scoped>
.grid-cell {
  display: table-cell;
  text-align: center;
  vertical-align: top;
  border-right: 1px solid #333;
  border-bottom: 1px solid #333;
  color: #e0e0e0;
  box-sizing: border-box;
  font-size: 0.9rem;
  padding: 0;
  overflow: hidden;
}

.index-cell {
  width: 5%;
}

.cell-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  background-color: #252525;
  font-size: 0.9rem;
  color: #999;
  font-weight: 500;
  padding-top: 8px;
}
</style>



 




 
