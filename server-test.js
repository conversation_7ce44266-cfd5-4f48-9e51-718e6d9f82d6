const http = require('http');

// 测试服务器是否运行
function testServer(port) {
  return new Promise((resolve, reject) => {
    const req = http.get(`http://localhost:${port}/api/health`, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          console.log(`服务器响应(端口${port}):`, data);
          const response = JSON.parse(data);
          resolve({ success: true, data: response });
        } catch (e) {
          console.error(`解析响应出错(端口${port}):`, e.message);
          reject(new Error(`解析响应失败: ${e.message}`));
        }
      });
    });

    req.on('error', (err) => {
      console.error(`无法连接到端口${port}:`, err.message);
      reject(new Error(`连接失败: ${err.message}`));
    });

    req.setTimeout(2000, () => {
      req.abort();
      console.error(`连接端口${port}超时`);
      reject(new Error('连接超时'));
    });
  });
}

// 测试本地通信API
function testLocalCommunication(port) {
  return new Promise((resolve, reject) => {
    const req = http.get(`http://localhost:${port}/api/local/list-folders?base=draft`, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          console.log(`本地通信API响应(端口${port}):`, data);
          const response = JSON.parse(data);
          resolve({ success: true, data: response });
        } catch (e) {
          console.error(`解析本地通信API响应出错(端口${port}):`, e.message);
          reject(new Error(`解析响应失败: ${e.message}`));
        }
      });
    });

    req.on('error', (err) => {
      console.error(`无法连接到本地通信API(端口${port}):`, err.message);
      reject(new Error(`连接失败: ${err.message}`));
    });

    req.setTimeout(2000, () => {
      req.abort();
      console.error(`连接本地通信API(端口${port})超时`);
      reject(new Error('连接超时'));
    });
  });
}

// 测试不同端口
async function runTests() {
  console.log('==== 开始测试服务器连接 ====');
  
  const ports = [8080, 8089];
  
  for (const port of ports) {
    try {
      console.log(`测试端口 ${port}...`);
      const result = await testServer(port);
      console.log(`端口 ${port} 测试成功:`, JSON.stringify(result, null, 2));
      
      // 如果服务器连接成功，测试本地通信API
      try {
        console.log(`测试端口 ${port} 的本地通信API...`);
        const apiResult = await testLocalCommunication(port);
        console.log(`端口 ${port} 的本地通信API测试成功:`, JSON.stringify(apiResult, null, 2));
      } catch (apiError) {
        console.error(`端口 ${port} 的本地通信API测试失败:`, apiError.message);
      }
    } catch (error) {
      console.error(`端口 ${port} 测试失败:`, error.message);
    }
  }
  
  console.log('==== 测试完成 ====');
}

runTests(); 