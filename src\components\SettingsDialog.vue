<template>
  <BaseDialog
    :model-value="modelValue"
    @update:modelValue="updateModelValue"
    dialog-class="settings-dialog"
    title="系统设置"
  >
    <div class="settings-container">
      <!-- 左侧导航 -->
      <div class="settings-sidebar">
        <div class="sidebar-menu">
          <div 
            v-for="(item, index) in activeMenuItems" 
            :key="index" 
            :class="['menu-item', { active: activeMenu === item.id }]"
            @click="activeMenu = item.id"
          >
            <i :class="item.icon" />
            <span>{{ item.label }}</span>
          </div>
        </div>
      </div>
      
      <!-- 右侧内容区 -->
      <div class="settings-content">
        <!-- LLM 设置 -->
        <div
          v-if="activeMenu === 'llm'"
          class="settings-panel"
        >
          <h2 class="panel-title">
            大语言模型设置
          </h2>
          
          <div class="settings-form">
            <div class="form-row">
              <div class="form-group">
                <label for="llm-provider">服务提供商</label>
                <select
                  id="llm-provider"
                  v-model="settings.llm.provider"
                  class="form-input"
                >
                  <option value="openrouter">
                    Open Router
                  </option>
                  <option value="google">
                    Google Gemini
                  </option>
                  <option value="openai">
                    OpenAI
                  </option>
                  <option value="anthropic">
                    Anthropic
                  </option>
                  <option value="local">
                    本地模型
                  </option>
                </select>
              </div>
              
              <div class="form-group">
                <label for="default-model">默认模型</label>
                <select
                  id="default-model"
                  v-model="settings.llm.defaultModel"
                  class="form-input"
                >
                  <template v-if="settings.llm.provider === 'openrouter'">
                    <option value="deepseek/deepseek-r1:free">
                      DeepSeek R1 (免费)
                    </option>
                    <option value="qwen/qwen3-235b-a22b:free">
                      Qwen3-235B-A22B (免费)
                    </option>
                    <option value="tngtech/deepseek-r1t-chimera:free">
                      DeepSeek R1T Chimera (免费)
                    </option>
                    <option value="thudm/glm-4-9b:free">
                      GLM-4-9B (免费)
                    </option>
                  </template>
                  
                  <template v-else-if="settings.llm.provider === 'google'">
                    <option v-for="model in activeGeminiModels" :key="model.id" :value="model.id">
                      {{ model.displayName }}
                    </option>
                    <!-- 如果没有获取到模型列表，显示以下默认选项 -->
                    <option v-if="!activeGeminiModels.length" value="gemini-pro">
                      Gemini Pro
                    </option>
                    <option v-if="!activeGeminiModels.length" value="gemini-1.5-pro">
                      Gemini 1.5 Pro
                    </option>
                  </template>
                  
                  <template v-else-if="settings.llm.provider === 'openai'">
                    <option value="gpt-3.5-turbo">
                      GPT-3.5 Turbo
                    </option>
                    <option value="gpt-4">
                      GPT-4
                    </option>
                    <option value="gpt-4-turbo">
                      GPT-4 Turbo
                    </option>
                    <option value="gpt-4o">
                      GPT-4o
                    </option>
                  </template>
                  
                  <template v-else-if="settings.llm.provider === 'anthropic'">
                    <option value="claude-3-haiku-20240307">
                      Claude 3 Haiku
                    </option>
                    <option value="claude-3-sonnet-20240229">
                      Claude 3 Sonnet
                    </option>
                    <option value="claude-3-opus-20240229">
                      Claude 3 Opus
                    </option>
                  </template>
                  
                  <template v-else-if="settings.llm.provider === 'local'">
                    <option value="local-model">
                      本地默认模型
                    </option>
                    <option value="llama3">
                      Llama 3
                    </option>
                    <option value="mistral">
                      Mistral
                    </option>
                  </template>
                </select>
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label for="temperature">温度 (创造性)</label>
                <div class="slider-container">
                  <input 
                    type="range" 
                    id="temperature" 
                    v-model.number="settings.llm.temperature" 
                    min="0" 
                    max="1" 
                    step="0.1" 
                    class="form-slider"
                  >
                  <span class="slider-value">{{ settings.llm.temperature }}</span>
                </div>
                <div class="form-hint">
                  较低的值使结果更确定，较高的值使结果更多样化和创造性
                </div>
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label for="max-output-tokens">最大输出Token数</label>
                <input
                  type="number"
                  id="max-output-tokens"
                  v-model.number="settings.llm.maxOutputTokens"
                  class="form-input"
                  min="1"
                  max="65536"
                  placeholder="4096"
                >
                <div class="form-hint">
                  控制每次AI生成的最大输出Token数。建议4096~65536，过大可能导致API报错或消耗过多配额。
                </div>
              </div>
            </div>
            
            <!-- API密钥输入 -->
            <div class="form-row">
              <div class="form-group api-key-group">
                <label for="api-key">API密钥</label>
                <div class="api-input-group">
                  <div class="input-wrapper">
                    <input 
                      :type="showPassword ? 'text' : 'password'" 
                      id="api-key" 
                      v-model="getCurrentApiKey" 
                      class="form-input with-icon"
                      :placeholder="`请输入${settings.llm.provider}的API密钥`"
                    >
                    <button 
                      class="password-toggle-button" 
                      type="button" 
                      @click="showPassword = !showPassword"
                      :title="showPassword ? '隐藏密钥' : '显示密钥'"
                    >
                      <i :class="showPassword ? 'ri-eye-off-line' : 'ri-eye-line'" />
                    </button>
                  </div>
                  <button
                    class="test-api-button"
                    @click="testApiKey"
                    :disabled="apiTestStatus === 'loading'"
                  >
                    <span v-if="apiTestStatus === 'loading'">
                      <span class="loading-indicator" /> 测试中...
                    </span>
                    <span v-else-if="apiTestStatus === 'success'">
                      <span class="status-indicator success" /> 已连接
                    </span>
                    <span v-else-if="apiTestStatus === 'error'">
                      <span class="status-indicator error" /> 测试
                    </span>
                    <span v-else>
                      <i class="ri-link-m" /> 测试连接
                    </span>
                  </button>
                  <button
                    v-if="settings.llm.provider === 'openrouter'"
                    class="test-api-button"
                    @click="checkOpenRouterCredit"
                  >
                    <i class="ri-money-dollar-circle-line" /> 查看余额
                  </button>
                </div>
                
                <!-- API测试结果消息 -->
                <div 
                  v-if="apiTestStatus && apiTestMessage" 
                  :class="['api-test-message', apiTestStatus]"
                >
                  <i :class="apiTestStatus === 'success' ? 'ri-checkbox-circle-line' : 'ri-error-warning-line'" />
                  <span>{{ apiTestMessage }}</span>
                </div>
                
                <div class="form-hint">
                  请输入您的API密钥，用于连接到{{ settings.llm.provider }}服务。系统不会将您的密钥发送给第三方。
                </div>
                <div v-if="settings.llm.provider === 'openrouter' && openrouterCreditInfo" class="form-hint" style="white-space: pre-line;">
                  {{ openrouterCreditInfo }}
                </div>
              </div>
            </div>
            
            <div
              class="form-row"
              v-if="settings.llm.provider === 'local'"
            >
              <div class="form-group api-key-group">
                <label for="local-url">本地模型API地址</label>
                <input 
                  type="text" 
                  id="local-url" 
                  v-model="settings.llm.localUrl" 
                  class="form-input"
                  placeholder="http://localhost:8080"
                >
              </div>
            </div>
          </div>
          
          <!-- Google Gemini 模型选择 -->
          <div
            v-if="settings.llm.provider === 'google'"
            class="model-selection"
          >
            <h3>选择 Gemini 模型</h3>
            <div
              v-if="isLoadingModels"
              class="loading-models"
            >
              <div class="loading-spinner" />
              <span>正在加载可用模型...</span>
            </div>
            <div
              v-else-if="activeGeminiModels.length === 0"
              class="no-models-message"
            >
              未找到可用的Gemini模型，请检查API密钥权限。
            </div>
            <div
              v-else
              class="model-list"
            >
              <div
                v-for="model in activeGeminiModels" 
                :key="model.id" 
                class="model-card"
                :class="{ 'selected': settings.llm.defaultModel === model.id }"
                @click="selectModel(model.id)"
              >
                <h4>{{ model.displayName || model.name }}</h4>
                <p>{{ model.description }}</p>
                <div class="capabilities">
                  <span
                    v-for="cap in model.capabilities"
                    :key="cap"
                    class="capability-tag"
                  >
                    {{ cap }}
                  </span>
                </div>
                <div class="model-limits">
                  <span>输入上限: {{ model.inputTokenLimit.toLocaleString() }} tokens</span>
                  <span>输出上限: {{ model.outputTokenLimit.toLocaleString() }} tokens</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="action-buttons">
            <button
              class="save-button"
              @click="saveSettings"
            >
              保存设置
            </button>
            <button
              class="cancel-button"
              @click="resetSettings"
            >
              重置
            </button>
          </div>
        </div>
        
        <!-- 当前版本只实现LLM设置 -->
        <div
          v-else
          class="settings-panel"
        >
          <h2 class="panel-title">
            选择设置项
          </h2>
          <div class="placeholder-text">
            请从左侧选择大语言模型设置
          </div>
        </div>
      </div>
    </div>
  </BaseDialog>
</template>

<script>
import BaseDialog from './BaseDialog.vue';

export default {
  name: 'SettingsDialog',
  components: {
    BaseDialog
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'save-settings'],
  data() {
    return {
      activeMenu: 'llm',
      apiTestStatus: '', // 'loading', 'success', 'error'
      apiTestMessage: '', // 添加测试结果消息
      showPassword: false, // 控制密码显示
      isLoadingModels: false, // 加载模型状态
      openrouterCreditInfo: '', // 新增：余额和速率信息
      activeMenuItems: [
        { id: 'llm', label: '大语言模型', icon: 'ri-robot-line' }
      ],
      settings: {
        llm: {
          provider: 'openrouter',
          defaultModel: 'deepseek/deepseek-r1:free',
          temperature: 0.7,
          localUrl: 'http://localhost:8080',
          // 为每个提供商添加独立的API密钥
          openrouterApiKey: '',
          googleApiKey: '',
          openaiApiKey: '',
          anthropicApiKey: '',
          localApiKey: '',
          // Gemini模型列表 - 将通过API动态获取
          geminiModels: [],
          maxOutputTokens: 4096
        }
      },
      defaultSettings: {
        theme: 'dark',
        language: 'zh-CN',
        autoSave: true,
        llm: {
          provider: 'openrouter',
          defaultModel: 'deepseek/deepseek-r1:free',
          temperature: 0.7,
          localUrl: 'http://localhost:8080',
          // 为每个提供商添加独立的API密钥
          openrouterApiKey: '',
          googleApiKey: '',
          openaiApiKey: '',
          anthropicApiKey: '',
          localApiKey: '',
          // Gemini模型列表 - 将通过API动态获取
          geminiModels: [],
          maxOutputTokens: 4096
        }
      }
    };
  },
  computed: {
    getCurrentApiKey: {
      get() {
        switch(this.settings.llm.provider) {
          case 'openrouter': return this.settings.llm.openrouterApiKey;
          case 'google': return this.settings.llm.googleApiKey;
          case 'openai': return this.settings.llm.openaiApiKey;
          case 'anthropic': return this.settings.llm.anthropicApiKey;
          case 'local': return this.settings.llm.localApiKey;
          default: return '';
        }
      },
      set(value) {
        switch(this.settings.llm.provider) {
          case 'openrouter': this.settings.llm.openrouterApiKey = value; break;
          case 'google': this.settings.llm.googleApiKey = value; break;
          case 'openai': this.settings.llm.openaiApiKey = value; break;
          case 'anthropic': this.settings.llm.anthropicApiKey = value; break;
          case 'local': this.settings.llm.localApiKey = value; break;
        }
      }
    },
    // 计算可用的Gemini模型列表，过滤掉已弃用的模型
    activeGeminiModels() {
      // 如果模型列表为空，返回空数组
      if (!this.settings.llm.geminiModels || this.settings.llm.geminiModels.length === 0) {
        return [];
      }
      
      // 过滤掉已弃用的模型
      return this.settings.llm.geminiModels.filter(model => {
        // 如果描述中包含deprecated，则认为该模型已弃用
        const isDeprecated = model.description && 
                           model.description.toLowerCase().includes('deprecated');
        
        // 只返回未弃用的模型
        return !isDeprecated;
      }).sort((a, b) => {
        // 优先显示较新的模型
        if (a.id.includes('1.5-pro') && !b.id.includes('1.5-pro')) return -1;
        if (!a.id.includes('1.5-pro') && b.id.includes('1.5-pro')) return 1;
        if (a.id.includes('1.5') && !b.id.includes('1.5')) return -1;
        if (!a.id.includes('1.5') && b.id.includes('1.5')) return 1;
        return 0;
      });
    }
  },
  methods: {
    updateModelValue(value) {
      this.$emit('update:modelValue', value);
    },
    // 获取Google模型列表
    async fetchGoogleModels() {
      // 如果对话框未打开，直接返回，不执行API调用
      if (!this.modelValue) {
        console.log('对话框未打开，跳过获取模型列表');
        return [];
      }
      
      if (!this.settings.llm.googleApiKey) {
        this.showApiError('请先输入API密钥以获取可用模型');
        return [];
      }
      
      this.isLoadingModels = true;
      console.log('正在获取Google模型列表...');
      
      try {
        const url = `https://generativelanguage.googleapis.com/v1beta/models?key=${this.settings.llm.googleApiKey}`;
        const response = await fetch(url);
        
        if (!response.ok) throw new Error(`API请求失败: ${response.status}`);
        
        const data = await response.json();
        console.log('收到模型数据:', JSON.stringify(data).substring(0, 300) + '...');
        
        if (data && data.models && Array.isArray(data.models)) {
          const geminiModels = this.filterGeminiModels(data.models);
          console.log('找到', geminiModels.length, '个Gemini模型');
          
          this.settings.llm.geminiModels = this.processGeminiModels(geminiModels);
          console.log('处理后的模型列表:', this.settings.llm.geminiModels);
          
          this.updateDefaultGeminiModel();
          
          this.showApiSuccess('获取模型列表成功');
          return this.settings.llm.geminiModels;
        } else {
          throw new Error('服务器响应中没有模型数据');
        }
      } catch (error) {
        this.showApiError(`获取模型列表失败: ${error.message}`);
        return [];
      } finally {
        this.isLoadingModels = false;
      }
    },
    
    // 过滤Gemini模型
    filterGeminiModels(models) {
      return models.filter(model => 
        model.name.includes('gemini') && 
        model.supportedGenerationMethods && 
        model.supportedGenerationMethods.includes('generateContent')
      );
    },
    
    // 处理Gemini模型信息
    processGeminiModels(geminiModels) {
      // 模型列表预处理
      const processedModels = geminiModels.map(model => {
        const id = model.name.split('/').pop(); 
        
        // 强制使用根据ID生成的displayName，以确保与ID的一致性
        const finalDisplayName = this.formatModelName(id); 

        // 如果原始Google API提供的displayName与我们生成的finalDisplayName不同，
        // 打印一个警告。这有助于我们了解Google API返回的数据何时可能存在不一致。
        if (model.displayName && model.displayName !== finalDisplayName) {
          console.warn(
            `模型ID "${id}": Google API 提供的原始 displayName ("${model.displayName}") ` +
            `与我们基于ID生成的名称 ("${finalDisplayName}") 不一致。将统一使用后者。`
          );
        }

        return {
          name: model.name, 
          id,              
          displayName: finalDisplayName, 
          description: model.description || this.getModelDescription(id), 
          capabilities: this.getModelCapabilities(id),
          inputTokenLimit: model.inputTokenLimit || 30720, 
          outputTokenLimit: model.outputTokenLimit || 2048  
        };
      });
      
      console.log('处理后的模型列表 (强制使用基于ID的displayName):', processedModels);
      return processedModels;
    },
    
    // 格式化模型名称
    formatModelName(id) {
      const nameParts = id.split('-');
      return nameParts.map(part => part.charAt(0).toUpperCase() + part.slice(1)).join(' ');
    },
    
    // 获取模型描述
    getModelDescription(id) {
      if (id.includes('pro')) {
        return '高性能模型，适用于复杂任务和推理';
      } else if (id.includes('flash')) {
        return '快速响应模型，适合需要低延迟的应用场景';
      } else if (id.includes('vision')) {
        return '支持图像理解的多模态模型';
      }
      return 'Gemini语言模型';
    },
    
    // 获取模型能力
    getModelCapabilities(id) {
      const capabilities = ['文本生成', '对话'];
      if (id.includes('pro')) {
        capabilities.push('代码生成', '分析', '长文本处理');
      } else if (id.includes('flash')) {
        capabilities.push('快速响应');
      } else if (id.includes('vision')) {
        capabilities.push('图像理解');
      }
      return capabilities;
    },
    
    // 更新默认Gemini模型
    updateDefaultGeminiModel() {
      if (this.settings.llm.provider !== 'google') return;
      
      // 如果没有可用模型，不进行操作
      if (!this.settings.llm.geminiModels || this.settings.llm.geminiModels.length === 0) {
        console.log('没有可用的Gemini模型，无法设置默认模型');
        return;
      }
      
      // 优先排序模型，将最新的模型放在前面
      const sortedModels = [...this.settings.llm.geminiModels].sort((a, b) => {
        // 优先选择 1.5-pro 模型
        if (a.id.includes('1.5-pro') && !b.id.includes('1.5-pro')) return -1;
        if (!a.id.includes('1.5-pro') && b.id.includes('1.5-pro')) return 1;
        // 其次是 1.5 系列模型
        if (a.id.includes('1.5') && !b.id.includes('1.5')) return -1;
        if (!a.id.includes('1.5') && b.id.includes('1.5')) return 1;
        // 再其次是pro模型
        if (a.id.includes('pro') && !b.id.includes('pro')) return -1;
        if (!a.id.includes('pro') && b.id.includes('pro')) return 1;
        return 0;
      });
      
      // 检查当前默认模型是否在可用模型列表中
      const defaultModelIsAvailable = sortedModels.some(
        model => model.id === this.settings.llm.defaultModel
      );
      
      // 如果默认模型不可用，选择排序后的第一个模型
      if (!defaultModelIsAvailable) {
        const newDefaultModel = sortedModels[0].id;
        console.log(`当前默认模型不可用，将设置新的默认模型: ${newDefaultModel}`);
        this.settings.llm.defaultModel = newDefaultModel;
      } else {
        console.log(`当前默认模型可用: ${this.settings.llm.defaultModel}`);
      }
    },
    
    // 测试OpenRouter API
    async testOpenRouterApi(apiKey) {
      const endpoint = 'https://openrouter.ai/api/v1/chat/completions';
      const requestData = {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'HTTP-Referer': location.origin,
          'X-Title': 'Voice Comic Generator',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: this.settings.llm.defaultModel,
          messages: [{ role: 'user', content: 'Please reply with "Hello" only.' }],
          max_tokens: 10
        })
      };
      
      const response = await fetch(endpoint, requestData);
      const data = await response.json();
      
      if (response.ok && data.choices && data.choices[0] && data.choices[0].message) {
        const aiResponse = data.choices[0].message.content.trim();
        this.showApiSuccess(`API测试成功! AI回复: "${aiResponse}"`);
      } else {
        const errorMessage = data.error ? 
          (data.error.message || `错误: ${data.error.type || data.error.code || '未知'}`) : 
          `请求失败: ${response.status} ${response.statusText}`;
        throw new Error(errorMessage);
      }
    },
    
    // 测试Google API
    async testGoogleApi(apiKey) {
      try {
        // 先获取最新的模型列表
        await this.fetchGoogleModels();
        
        if (this.settings.llm.geminiModels.length === 0) {
          throw new Error('无法获取可用的Gemini模型');
        }
        
        // 选择一个可用模型进行测试
        const testModel = this.selectTestModel();
        console.log('选择模型进行测试:', testModel);
        
        const responseData = await this.executeGoogleModelTest(testModel, apiKey);
        await this.processGoogleApiResponse(responseData.response, responseData.data);
      } catch (error) {
        console.error('测试Google API失败:', error);
        throw new Error(`测试Google API失败: ${error.message}`);
      }
    },
    
    // 选择测试用的Google模型
    selectTestModel() {
      console.log('开始选择测试模型...');
      
      // 第一步：先检查我们是否有模型列表
      if (!this.settings.llm.geminiModels || !Array.isArray(this.settings.llm.geminiModels) || this.settings.llm.geminiModels.length === 0) {
        console.error('没有可用的模型列表进行测试');
        throw new Error('没有可用的模型列表');
      }
      
      // 列出所有可用模型的ID进行调试
      console.log('所有可用模型:', this.settings.llm.geminiModels.map(m => m.id));
      
      // 首先尝试使用用户选择的模型
      let testModel = this.settings.llm.geminiModels.find(m => m.id === this.settings.llm.defaultModel);
      console.log('默认模型查询结果:', testModel ? testModel.id : '未找到');
      
      // 如果没有找到用户选择的模型，按优先级选择一个稳定的模型
      if (!testModel) {
        // 先尝试稳定版本的模型，而不是实验性模型
        testModel = 
          // 1.5 Pro 系列，首选
          this.settings.llm.geminiModels.find(m => m.id === 'gemini-1.5-pro') ||
          this.settings.llm.geminiModels.find(m => m.id === 'gemini-1.5-pro-latest') ||
          this.settings.llm.geminiModels.find(m => m.id === 'gemini-1.5-pro-001') ||
          this.settings.llm.geminiModels.find(m => m.id === 'gemini-1.5-pro-002') ||
          
          // Flash 系列，次选
          this.settings.llm.geminiModels.find(m => m.id === 'gemini-1.5-flash') ||
          this.settings.llm.geminiModels.find(m => m.id === 'gemini-1.5-flash-latest') ||
          this.settings.llm.geminiModels.find(m => m.id === 'gemini-1.5-flash-001') ||
          
          // 其他可用模型
          this.settings.llm.geminiModels.find(m => m.id.includes('1.5-pro')) ||
          this.settings.llm.geminiModels.find(m => m.id.includes('1.5-flash')) ||
          this.settings.llm.geminiModels.find(m => m.id.includes('gemini-pro'));
        
        // 如果依然没找到，使用第一个可用模型
        if (!testModel) {
          testModel = this.settings.llm.geminiModels[0];
          console.log('没有找到匹配的模型，使用第一个可用模型:', testModel.id);
        } else {
          console.log('选择到替代模型:', testModel.id);
        }
      } else {
        console.log('使用用户选择的默认模型:', testModel.id);
      }
      
      return testModel;
    },
    
    // 执行Google模型测试
    async executeGoogleModelTest(testModel, apiKey) {
      // 打印测试模型信息便于调试
      console.log('测试模型详情:', JSON.stringify(testModel));
      
      // 确保模型路径正确，如果缺少"models/"前缀则添加
      let modelPath = testModel.name;
      if (!modelPath.startsWith('models/')) {
        modelPath = `models/${testModel.id}`;
      }
      
      console.log('使用模型路径:', modelPath);
      const endpoint = `https://generativelanguage.googleapis.com/v1beta/${modelPath}:generateContent?key=${apiKey}`;
      console.log('请求端点:', endpoint);
      
      const requestBody = {
        contents: [{ 
          parts: [{ text: '简单测试，请直接回复"测试成功"' }] 
        }],
        generationConfig: {
          temperature: 0,
          maxOutputTokens: 50,
        }
      };
      
      console.log('发送请求体:', JSON.stringify(requestBody));
      
      const requestData = {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      };
      
      try {
        const response = await fetch(endpoint, requestData);
        const data = await response.json();
        console.log('API响应:', JSON.stringify(data));
        return { response, data };
      } catch (error) {
        console.error('测试请求错误:', error);
        throw error;
      }
    },
    
    // 处理Google API响应
    async processGoogleApiResponse(response, data) {
      console.log('处理响应状态码:', response.status);
      
      // 如果状态码非200，则认为是错误
      if (!response.ok) {
        const errorMessage = data.error ? 
          data.error.message || `错误: ${data.error.code || '未知'}` : 
          `请求失败: ${response.status} ${response.statusText}`;
        console.error('响应错误:', errorMessage);
        throw new Error(errorMessage);
      }
      
      // 打印完整的响应数据，便于调试
      console.log('收到完整响应数据:', JSON.stringify(data));
      
      // 如果响应码是200，我们就认为 API 连接测试成功
      // 即使没有具体的内容返回，只要服务器响应正常就认为测试成功
      if (data.candidates && Array.isArray(data.candidates)) {
        let aiResponse = '';
        
        // 尝试从不同格式的响应中提取文本
        if (data.candidates[0]) {
          if (data.candidates[0].content && data.candidates[0].content.parts && data.candidates[0].content.parts.length > 0) {
            // 新版API响应格式
            aiResponse = data.candidates[0].content.parts[0].text || '';
            console.log('从响应中提取的文本(新格式):', aiResponse);
          } else if (data.candidates[0].text) {
            // 旧版API响应格式
            aiResponse = data.candidates[0].text;
            console.log('从响应中提取的文本(旧格式):', aiResponse);
          }
        }
        
        // 即使得到空字符串也认为测试成功 - 因为 API 连接正常工作
        if (aiResponse) {
          // 如果有内容则显示内容
          const successMsg = aiResponse.includes('测试成功') ? 
            `API测试成功! AI回复: "${aiResponse}"` : 
            `API测试成功! (得到回复: "${aiResponse.substring(0, 30)}${aiResponse.length > 30 ? '...' : ''}")`;
          this.showApiSuccess(successMsg);
        } else {
          // 空响应也算成功，因为我们在测试 API 连接，而不是模型输出
          this.showApiSuccess(`API测试成功! (请求成功但没有返回内容)`);
        }
        return;
      }
      
      // 只有当没有candidates字段时才认为测试失败
      console.warn('没有收到正确的API响应格式');
      throw new Error('测试失败: API响应格式异常');
    },
    
    // 测试本地API
    async testLocalApi() {
      const endpoint = '/api/generate';
      const requestData = {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          model: 'local',
          prompt: '你好，请回复"你好"',
          options: { max_tokens: 10, temperature: 0.7 },
          history: []
        })
      };
      
      const response = await fetch(endpoint, requestData);
      const data = await response.json();
      
      if (response.ok && data.choices && data.choices[0] && data.choices[0].message) {
        const aiResponse = data.choices[0].message.content.trim();
        this.showApiSuccess(`API测试成功! AI回复: "${aiResponse}"`);
      } else {
        const errorMessage = data.error ? 
          (data.error.message || `错误: ${data.error.type || data.error.code || '未知'}`) : 
          `请求失败: ${response.status} ${response.statusText}`;
        throw new Error(errorMessage);
      }
    },
    
    // 显示API成功消息
    showApiSuccess(message) {
      this.apiTestStatus = 'success';
      this.apiTestMessage = message;
      setTimeout(() => {
        if (this.apiTestStatus === 'success') {
          this.apiTestStatus = '';
          this.apiTestMessage = '';
        }
      }, 5000);
    },
    
    // 显示API错误消息
    showApiError(message) {
      this.apiTestStatus = 'error';
      this.apiTestMessage = message;
    },
    async testApiKey() {
      const apiKey = this.getCurrentApiKey;
      
      if (!apiKey) {
        this.showApiError('请先输入API密钥');
        return;
      }
      
      this.apiTestStatus = 'loading';
      this.apiTestMessage = '正在测试API密钥...';
      
      try {
        switch (this.settings.llm.provider) {
          case 'openrouter':
            await this.testOpenRouterApi(apiKey);
            break;
            
          case 'google':
            await this.testGoogleApi(apiKey);
            break;
            
          case 'local':
            await this.testLocalApi();
            break;
          
          default:
            throw new Error(`未知的API提供商: ${this.settings.llm.provider}`);
        }
      } catch (error) {
        this.showApiError(`测试出错: ${error.message}`);
      }
    },
    async saveSettings() {
      try {
        // 构建要保存的数据
        // 创建一个新的设置对象，不保存 geminiModels 数组
        const settingsToSave = JSON.parse(JSON.stringify(this.settings));
        console.log('[SettingsDialog saveSettings] Values in this.settings.llm before creating settingsToSave:', JSON.stringify(this.settings.llm)); // Log full llm object
        console.log('[SettingsDialog saveSettings] defaultModel in settingsToSave before API call:', settingsToSave.llm.defaultModel, 'Provider:', settingsToSave.llm.provider);

        // 从保存对象中移除 geminiModels，因为这些模型应该通过 API 动态获取
        if (settingsToSave.llm && settingsToSave.llm.geminiModels) {
          console.log('保存前移除模型列表，避免不必要的存储');
          delete settingsToSave.llm.geminiModels;
        }
        
        // 尝试发送到服务器保存到用户目录
        const response = await fetch('/api/local/save-user-settings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            settings: settingsToSave,
            filename: 'usersettings.json'
          })
        });
        
        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`服务器保存设置失败: ${errorText}`);
        }
        
        const result = await response.json();
        
        if (!result.success) {
          throw new Error('服务器返回保存失败状态');
        }
        
        // 清除localStorage中的设置，确保下次从服务器获取最新设置
        console.log('清除localStorage中的设置缓存，确保下次使用最新设置');
        localStorage.removeItem('usersettings');
        
        // 发出设置已保存的事件
        this.$emit('save-settings', this.settings);
        
        // 显示成功消息
        alert(result.path ? `设置已保存到 ${result.path}` : '设置保存成功');
        
        // 关闭对话框
        this.updateModelValue(false);
      } catch (error) {
        console.error('保存设置出错:', error);
        
        // 显示错误消息
        alert(`保存设置失败: ${error.message}`);
      }
    },
    resetSettings() {
      this.settings = JSON.parse(JSON.stringify(this.defaultSettings));
    },
    async loadSavedSettings() {
      try {
        // 从服务器加载设置
        const response = await fetch('/api/local/load-user-settings?filename=usersettings.json');
        
        if (!response.ok) {
          console.warn('无法加载用户设置，服务器响应: ', response.status, response.statusText);
          // 无法从服务器加载，使用默认设置
          console.log('使用默认设置');
          return;
        }
        
        const result = await response.json();
        
        if (!result.success || !result.settings) {
          console.warn('服务器返回无效的设置数据');
          return;
        }
        
        // 直接使用result.settings，它已经是一个对象，不需要再解析
        const userSettings = result.settings;
        
        // 特别处理: 确保缓存的 geminiModels 不会被加载(如果存在)
        if (userSettings.llm && userSettings.llm.geminiModels) {
          console.log('忽略保存的模型列表，将使用API获取最新模型');
          delete userSettings.llm.geminiModels;
        }
        
        // 保存当前的geminiModels列表（如果已经获取过）
        const currentGeminiModels = this.settings.llm.geminiModels || [];
        
        // 合并保存的设置与默认设置，确保版本更新时添加的新设置项不会丢失
        this.settings = {
          ...this.defaultSettings,
          ...userSettings,
          llm: {
            ...this.defaultSettings.llm,
            ...(userSettings.llm || {}),
            // 重新添加当前的模型列表，或者初始化为空数组
            geminiModels: currentGeminiModels
          }
        };
        
        console.log('从用户目录加载设置成功');
      } catch (error) {
        console.error('加载设置时出错:', error);
        alert(`加载设置失败: ${error.message}`);
      }
    },
    selectModel(modelId) {
      this.settings.llm.defaultModel = modelId;
      console.log('已选择模型:', modelId);
    },
    async checkOpenRouterCredit() {
      const apiKey = this.settings.llm.openrouterApiKey;
      if (!apiKey) {
        this.openrouterCreditInfo = '请先输入OpenRouter的API密钥';
        return;
      }
      try {
        const response = await fetch('https://openrouter.ai/api/v1/auth/key', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${apiKey}`
          }
        });
        if (!response.ok) throw new Error('请求失败: ' + response.status);
        const data = await response.json();
        if (data && data.data) {
          const { limit, usage, is_free_tier, rate_limit } = data.data;
          let msg = '';
          if (limit === null) {
            msg = '当前API Key额度为无限制 (unlimited)。';
          } else {
            const remain = limit - usage;
            msg = `OpenRouter API Key 剩余额度：${remain} / ${limit} (已用 ${usage})`;
          }
          if (is_free_tier) {
            msg += '\n(当前为免费额度)';
          }
          if (rate_limit && typeof rate_limit.requests === 'number' && rate_limit.interval) {
            msg += `\n速率限制：每${rate_limit.interval}最多${rate_limit.requests}次请求`;
          }
          this.openrouterCreditInfo = msg;
        } else {
          this.openrouterCreditInfo = '未能获取到余额信息';
        }
      } catch (e) {
        this.openrouterCreditInfo = '查询余额失败: ' + e.message;
      }
    }
  },
  watch: {
    'settings.llm.provider': {
      handler(newValue) {
        console.log('提供商变更为:', newValue);
        if (newValue === 'google' && this.settings.llm.googleApiKey) {
          // 当切换到Google且有API密钥时，获取可用模型
          console.log('自动获取Google模型列表...');
          this.fetchGoogleModels();
        }
      }
    },
    'modelValue': {
      handler(newValue) {
        // 当对话框显示时，如果当前提供商是Google并且有API密钥，则获取模型列表
        if (newValue && this.settings.llm.provider === 'google' && this.settings.llm.googleApiKey) {
          console.log('对话框显示，自动获取Google模型列表...');
          this.fetchGoogleModels();
        }
      }
    },
    'settings.llm.googleApiKey': {
      handler(newValue) {
        if (newValue && this.settings.llm.provider === 'google') {
          console.log('Google API密钥已更新，自动获取模型列表...');
          this.fetchGoogleModels();
        }
      }
    }
  },
  mounted() {
    this.loadSavedSettings();
    
    // 如果已经选择了Google并且有API密钥，则获取模型列表
    if (this.settings.llm.provider === 'google' && this.settings.llm.googleApiKey) {
      console.log('组件挂载时自动获取Google模型列表...');
      this.fetchGoogleModels();
    }

    // 如果是Google提供商但默认模型ID不在geminiModels列表中，则设置为第一个可用的非弃用模型
    if (this.settings.llm.provider === 'google') {
      if (this.activeGeminiModels.length > 0) {
        const modelIds = this.activeGeminiModels.map(m => m.id);
        if (!modelIds.includes(this.settings.llm.defaultModel)) {
          this.settings.llm.defaultModel = modelIds[0];
          console.log('设置默认模型为第一个可用的非弃用模型:', this.settings.llm.defaultModel);
        }
      } else if (this.settings.llm.googleApiKey) {
        // 如果有API密钥但没有可用模型，尝试获取模型列表
        this.fetchGoogleModels();
      }
    }
  }
};
</script>

<style scoped>
.settings-dialog {
  max-width: 85vw;
  width: 85vw;
  height: 90vh;
  margin: auto;
  overflow: hidden;
}

.settings-container {
  display: flex;
  height: 100%;
  background-color: #1d1d2c;
  border-radius: 8px;
  overflow: hidden;
  width: 100%;
}

.settings-sidebar {
  width: 240px;
  min-width: 240px;
  background-color: #181825;
  border-right: 1px solid #292945;
  padding: 1.8rem 0;
}

.sidebar-menu {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 0 1.8rem;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 1rem 1.8rem;
  color: #a6adc8;
  cursor: pointer;
  transition: all 0.2s ease;
}

.menu-item i {
  font-size: 1.4rem;
}

.menu-item:hover {
  background-color: #292945;
  color: #cdd6f4;
}

.menu-item.active {
  background-color: #292945;
  color: #f5c2e7;
  border-left: 3px solid #f5c2e7;
}

.settings-content {
  flex: 1;
  padding: 3rem 4rem;
  overflow-y: auto;
  overflow-x: hidden;
  width: 100%;
  min-width: 0; /* 防止flex子项溢出 */
}

.settings-panel {
  width: 100%;
  margin: 0 auto;
}

.panel-title {
  font-size: 1.8rem;
  color: #f5c2e7;
  margin-bottom: 2rem;
  padding-bottom: 0.8rem;
  border-bottom: 1px solid #292945;
}

.settings-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  width: 100%;
}

.form-row {
  display: flex;
  gap: 3rem;
  flex-wrap: wrap;
  margin-bottom: 2rem;
}

.form-group {
  flex: 1;
  min-width: 300px;
  margin-bottom: 1rem;
}

.api-key-group {
  flex: 2;
  min-width: 400px;
}

.form-group label {
  display: block;
  margin-bottom: 0.8rem;
  color: #cdd6f4;
  font-weight: bold;
  font-size: 1.1rem;
}

.form-input {
  width: 100%;
  min-width: 0;
  box-sizing: border-box;
  padding: 1.2rem;
  background-color: #232338;
  border: 1px solid #333344;
  border-radius: 8px;
  color: #cdd6f4;
  font-size: 1.2rem;
}

.form-input:focus {
  border-color: #f5c2e7;
  outline: none;
  box-shadow: 0 0 0 2px rgba(245, 194, 231, 0.2);
}

.form-hint {
  font-size: 0.95rem;
  color: #7f849c;
  margin-top: 0.8rem;
}

.slider-container {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 0.5rem 0;
}

.form-slider {
  flex: 1;
  -webkit-appearance: none;
  height: 8px;
  background: #333344;
  border-radius: 4px;
  outline: none;
}

.form-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background: #f5c2e7;
  cursor: pointer;
  transition: all 0.2s ease;
}

.form-slider::-moz-range-thumb {
  appearance: none;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background: #f5c2e7;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.form-slider::-webkit-slider-thumb:hover {
  background: #2dc0f0;
  transform: scale(1.2);
}

.slider-value {
  color: #f5c2e7;
  font-weight: bold;
  min-width: 2.5rem;
  text-align: center;
  font-size: 1.1rem;
}

.action-buttons {
  display: flex;
  gap: 1.5rem;
  margin-top: 3rem;
  justify-content: flex-end;
}

.save-button, .cancel-button {
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: bold;
  font-size: 1.05rem;
  cursor: pointer;
  transition: all 0.2s;
}

.save-button {
  background-color: #2dc0f0;
  color: #11111b;
  border: none;
}

.save-button:hover {
  background-color: #3ad7ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(45, 192, 240, 0.3);
}

.cancel-button {
  background-color: #333344;
  color: #cdd6f4;
  border: none;
}

.cancel-button:hover {
  background-color: #45475a;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(69, 71, 90, 0.3);
}

.placeholder-text {
  color: #7f849c;
  font-style: italic;
  margin-top: 3rem;
  text-align: center;
  font-size: 1.1rem;
}

.api-input-group {
  display: flex;
  gap: 1rem;
  width: 100%;
  align-items: center;
  position: relative;
}

.test-api-button {
  background-color: #333344;
  color: #cdd6f4;
  border: none;
  border-radius: 8px;
  padding: 0.8rem 1.5rem;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.test-api-button:hover {
  background-color: #45475a;
  transform: translateY(-2px);
}

.loading-indicator {
  display: inline-block;
  width: 14px;
  height: 14px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #cdd6f4;
  animation: spin 1s ease-in-out infinite;
}

.status-indicator {
  display: inline-block;
  width: 14px;
  height: 14px;
  border-radius: 50%;
}

.status-indicator.success {
  background-color: #2dc0f0;
  box-shadow: 0 0 10px #2dc0f0;
  animation: pulse 2s infinite;
}

.status-indicator.error {
  background-color: #f38ba8;
  box-shadow: 0 0 10px #f38ba8;
  animation: pulse 2s infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.6; }
  100% { opacity: 1; }
}

.input-wrapper {
  position: relative;
  width: 100%;
}
.form-input.with-icon {
  padding-right: 2.5rem; /* 为图标留出空间 */
}
.password-toggle-button {
  position: absolute;
  top: 50%;
  right: 0.75rem;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #7f849c;
  cursor: pointer;
  font-size: 1.2rem;
  padding: 0;
  transition: color 0.2s;
}
.password-toggle-button:hover {
  color: #f5c2e7;
}
.api-test-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.7rem;
  padding: 0.5rem 0.8rem;
  border-radius: 4px;
  font-size: 0.95rem;
}

.api-test-message.success {
  background-color: rgba(45, 192, 240, 0.15);
  color: #2dc0f0;
}

.api-test-message.error {
  background-color: rgba(243, 139, 168, 0.15);
  color: #f38ba8;
}

.api-test-message i {
  font-size: 1.1rem;
}

/* 响应式布局调整 */
@media (max-width: 1200px) {
  .settings-dialog {
    width: 95vw;
  }
  
  .settings-content {
    padding: 2rem;
  }
}

@media (max-width: 768px) {
  .settings-container {
    flex-direction: column;
  }
  
  .settings-sidebar {
    width: 100%;
    min-width: 0;
    padding: 1rem 0;
  }
  
  .settings-content {
    padding: 1.5rem;
  }
  
  .form-row {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .form-group {
    min-width: 100%;
  }
}

.model-selection {
  margin: 20px 0;
}

.model-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 15px;
}

.model-card {
  border: 1px solid #333344;
  border-radius: 8px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #232338;
}

.model-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.model-card.selected {
  border-color: #f5c2e7;
  background-color: rgba(245, 194, 231, 0.1);
}

.model-card h4 {
  margin: 0 0 10px 0;
  color: #cdd6f4;
}

.model-card p {
  margin: 0 0 15px 0;
  color: #a6adc8;
  font-size: 0.9em;
}

.capabilities {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 15px;
}

.capability-tag {
  background-color: #333344;
  color: #cdd6f4;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8em;
}

.model-limits {
  display: flex;
  flex-direction: column;
  gap: 5px;
  font-size: 0.8em;
  color: #a6adc8;
}

.loading-models {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
  padding: 0.5rem 0.8rem;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.1);
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #cdd6f4;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.no-models-message {
  padding: 1rem;
  background-color: rgba(243, 139, 168, 0.15);
  border-radius: 8px;
  color: #f38ba8;
  text-align: center;
  margin-top: 1rem;
}
</style> 