/**
 * SRT字幕解析模块
 * 负责解析SRT字幕文件并生成提示词
 */

/**
 * 解析SRT文件内容
 * @param {string} content SRT文件内容
 * @returns {Array} 解析后的字幕数组
 */
function parseSRT(content) {
  const subtitles = [];
  const blocks = content.trim().split(/\r?\n\r?\n/);
  
  blocks.forEach(block => {
    const lines = block.split(/\r?\n/);
    if (lines.length >= 3) {
      // 第一行是序号，第二行是时间，后面的是文本
      const index = parseInt(lines[0].trim(), 10);
      const timeCode = lines[1].trim();
      const text = lines.slice(2).join(' ').trim();
      
      // 解析时间码 "00:00:20,000 --> 00:00:25,000"
      const timeParts = timeCode.split(' --> ');
      if (timeParts.length === 2) {
        subtitles.push({
          index,
          startTime: timeParts[0],
          endTime: timeParts[1],
          text,
        });
      }
    }
  });
  
  return subtitles;
}

/**
 * 将时间码转换为毫秒
 * @param {string} timeCode 时间码 (格式: 00:00:20,000)
 * @returns {number} 毫秒
 */
function timeCodeToMs(timeCode) {
  const [time, ms] = timeCode.split(',');
  const [hours, minutes, seconds] = time.split(':').map(Number);
  
  return (hours * 3600 + minutes * 60 + seconds) * 1000 + parseInt(ms, 10);
}

/**
 * 检查字幕时间是否有重叠
 * @param {Array} subtitles 字幕数组
 * @returns {Array} 修复后的字幕数组
 */
function fixOverlappingSubtitles(subtitles) {
  if (!subtitles || subtitles.length <= 1) return subtitles;
  
  const fixed = [subtitles[0]];
  
  for (let i = 1; i < subtitles.length; i++) {
    const current = subtitles[i];
    const previous = fixed[fixed.length - 1];
    
    const previousEndMs = timeCodeToMs(previous.endTime);
    const currentStartMs = timeCodeToMs(current.startTime);
    
    if (currentStartMs < previousEndMs) {
      // 有时间重叠，修正前一个字幕的结束时间
      previous.endTime = current.startTime;
    }
    
    fixed.push(current);
  }
  
  return fixed;
}

/**
 * 从字幕生成提示词
 * @param {Array} subtitles 字幕数组
 * @param {Object} options 选项
 * @returns {Array} 提示词数组
 */
function generatePrompts(subtitles, options = {}) {
  const {
    style = '漫画风格', 
    extraPrompt = '', 
    negativePrompt = 'bad quality, blurry, low resolution'
  } = options;
  
  // 修复重叠的字幕
  const fixedSubtitles = fixOverlappingSubtitles(subtitles);
  
  return fixedSubtitles.map(subtitle => {
    // 基本场景描述
    let prompt = `${style}，表现场景：${subtitle.text}`;
    
    // 添加额外提示词
    if (extraPrompt) {
      prompt += `, ${extraPrompt}`;
    }
    
    return {
      id: subtitle.index,
      startTime: subtitle.startTime,
      endTime: subtitle.endTime,
      text: subtitle.text,
      prompt,
      negativePrompt
    };
  });
}

module.exports = {
  parseSRT,
  generatePrompts,
  timeCodeToMs
}; 