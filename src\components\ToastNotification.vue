<template>
  <transition name="toast-fade">
    <div
      v-if="isVisible"
      class="toast-container"
      :class="type"
    >
      <div class="toast-icon">
        <i
          class="ri-error-warning-fill"
          v-if="type === 'error'"
        />
        <i
          class="ri-checkbox-circle-fill"
          v-else-if="type === 'success'"
        />
        <i
          class="ri-information-fill"
          v-else
        />
      </div>
      <div class="toast-content">
        <div class="toast-title">
          {{ title }}
        </div>
        <div class="toast-message">
          {{ message }}
        </div>
      </div>
      <div
        class="toast-close"
        @click="hide"
      >
        <i class="ri-close-line" />
      </div>
      <div class="toast-progress">
        <div
          class="progress-bar"
          :style="{width: progressWidth}"
        />
      </div>
    </div>
  </transition>
</template>



 




 
