{"LocalCommunication.vue": {"功能": ["通过 API 与本地文件系统交互，实现文件/文件夹的重命名、删除操作", "UI 输入原路径和新路径，点击按钮即可调用对应 API 完成重命名，输入文件夹名即可删除（删除会递归删除所有内容）", "操作结果和错误会在界面上实时反馈"], "如何使用": ["1. 在页面中引入 <LocalCommunication /> 组件即可使用。", "2. 输入要重命名的原文件夹名和新文件夹名，点击重命名按钮即可完成重命名。", "3. 输入要删除的文件夹名，点击删除按钮即可完成删除（会递归删除所有内容）。", "4. 组件内部通过 fetch 调用后端 API，前端无需关心具体实现。", "5. 需要后端实现 /api/local/rename-folder 和 /api/local/delete-folder 两个接口，分别对应重命名和删除功能。"], "依赖": ["无特殊依赖，直接使用 fetch 调用后端 API"], "注意事项": ["本组件仅为前端演示，实际操作本地文件需后端配合实现对应 API。", "操作路径请确保有权限，且路径格式与操作系统一致。", "删除操作不可恢复，请谨慎操作。"]}, "localCommunication": {"如何使用": ["1. 启动 Express 服务（node src/server.js），确保 projects 目录有写权限。", "2. 创建本地文件夹：POST /api/local/create-folder，body: { folderName: '文件夹名' }，返回 { success: true, folderPath } 或错误信息。", "3. 获取所有项目文件夹：GET /api/local/list-folders，返回文件夹名称数组。", "4. 前端可通过 fetch 或 axios 调用上述接口，实现项目的创建与列表展示。"]}, "dev_log": ["2024-04-27: 修复 ChapterManager.vue 中的 props mutation 问题，使用不可变更新和 emit 事件", "2024-04-27: 重构 ChapterManager.vue 中的章节管理逻辑，确保数据流的单向性", "2024-04-27: 修复 ProjectCreateDialog.vue 中的 modelValue prop mutation 问题", "2024-04-27: 统一后端主入口为 src/server.js，删除根目录 server.js，所有 npm run start/dev/dev-full 均指向 src/server.js。", "2024-04-27: 前端 vue.config.js 的 devServer.proxy.target 端口与后端保持一致（8080），避免端口错配导致 ECONNREFUSED。", "2024-04-27: 后端 Express 启动日志和所有 API 路由日志已增强，便于排查代理和业务问题。", "2024-04-27: 新增 src/assets/styles/button-hover.css 和 text-hover.css 两个全局/模块化样式文件，可直接用于按钮和文本悬停等UI动效开发。", "2024-04-27: main.js 添加全局 fetch 日志挂件，所有 API 请求和响应会自动输出到控制台，便于调试和排查。"], "BaseDrawer.vue": {"功能": ["通用抽屉（Drawer）模块，支持左侧滑入滑出动画，可插入任意内容，带关闭按钮。", "通过 v-model:show 控制显示/隐藏，适合多处复用，多个抽屉互不干扰。"], "如何使用": ["1. 在页面中引入 <BaseDrawer v-model:show=\"showDrawer\">...</BaseDrawer>。", "2. showDrawer 为 Boolean 变量，控制抽屉显示。", "3. 抽屉内容通过 <slot> 插入，可自定义按钮、表单等。", "4. 关闭按钮自动 emit update:show 事件，父组件可直接用 v-model 控制。"], "依赖": ["无特殊依赖，直接引入并注册组件即可。"], "注意事项": ["抽屉宽度、动画、内容均可自定义。", "适合做侧边栏、设置面板、详情弹窗等多种场景。"]}, "version": "1.0.0", "last_updated": "2024-03-21", "components": {"HomePage.vue": {"description": "主页面组件", "features": ["项目列表展示", "创建新项目功能", "功能卡片展示区域", "项目删除功能", "章节管理抽屉集成"], "dependencies": ["ProjectList", "ProjectCreateDialog", "ChapterManager", "BaseDrawer"], "styles": ["渐变色顶部栏", "功能卡片网格布局", "响应式设计"]}, "ProjectList.vue": {"description": "项目列表组件", "features": ["项目卡片展示", "项目标题编辑", "项目删除", "项目选择"], "events": ["select - 选择项目", "delete - 删除项目", "update - 更新项目列表"], "styles": ["卡片悬停效果", "文本悬停效果"]}, "ProjectCreateDialog.vue": {"description": "创建项目对话框", "features": ["项目名称输入", "项目描述输入", "表单验证"], "dependencies": ["BaseDialog"], "events": ["update:modelValue - 控制对话框显示", "created - 项目创建成功"]}, "ChapterManager.vue": {"description": "章节管理组件", "features": ["章节列表展示", "新建章节", "编辑章节标题", "删除章节", "章节排序（倒序）"], "styles": ["渐变色按钮", "表格布局", "动画效果"]}, "BaseDialog.vue": {"description": "基础对话框组件", "features": ["可自定义标题", "可自定义宽度", "点击遮罩关闭", "动画效果"], "slots": ["default - 主体内容", "footer - 底部按钮"], "styles": ["毛玻璃背景效果", "弹出动画"]}}, "utils": {"localCommunication": {"description": "本地文件系统通信工具", "functions": ["createFolder - 创建文件夹", "deleteFolder - 删除文件夹", "renameFolder - 重命名文件夹", "listFolders - 列出文件夹"]}}, "styles": {"theme": {"primary_colors": ["#29908b", "#a23a5a"], "background": "#181825", "text": {"primary": "#fff", "secondary": "#b5bfe2"}}}, "todo": ["添加项目封面图片上传功能", "添加项目描述编辑功能", "优化章节拖拽排序", "添加项目导出功能", "添加项目模板功能"], "known_issues": ["项目标题过长时可能导致卡片布局异常", "快速创建多个章节可能导致性能问题"]}