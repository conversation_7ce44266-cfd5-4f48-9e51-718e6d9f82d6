<template>
  <div class="grid-row header-row">
    <div class="grid-cell select-cell-header">
      <button class="select-header-btn">
        选中
      </button>
    </div>
    <div class="grid-cell index-header">
      序号
    </div>
    <div class="grid-cell description-header">
      原文描述
    </div>
    <div class="grid-cell tags-header">
      标签
    </div>
    <div class="grid-cell keyword-header">
      画面关键词
    </div>
    <div class="grid-cell image-header">
      本镜配图
    </div>
    <div class="grid-cell optional-image-header">
      可选图
    </div>
    <div class="grid-cell operation-header">
      操作
    </div>
  </div>
</template>

<script>
export default {
  name: '<PERSON><PERSON><PERSON>ead<PERSON>'
}
</script>

<style scoped>
.grid-row {
  display: table-row;
}

.header-row {
  background-color: #1e1e1e;
  height: 40px;
  font-weight: bold;
}

.grid-cell {
  display: table-cell;
  text-align: center;
  vertical-align: top;
  border-right: 1px solid #333;
  border-bottom: 2px solid #444;
  color: #e0e0e0;
  box-sizing: border-box;
  font-size: 0.9rem;
  padding: 8px;
}

.select-cell-header {
  width: 5%;
}

.index-header {
  width: 5%;
}

.description-header {
  width: 15%;
}

.tags-header {
  width: 11.25%;
}

.keyword-header {
  width: 23.75%;
}

.image-header {
  width: 12%;
}

.optional-image-header {
  width: 15%;
}

.operation-header {
  width: 10%;
  border-right: none;
}

.select-header-btn {
  background: none;
  border: none;
  color: #e0e0e0;
  font-size: 0.9rem;
  cursor: pointer;
  width: 100%;
  text-align: center;
}
</style>



 




 
