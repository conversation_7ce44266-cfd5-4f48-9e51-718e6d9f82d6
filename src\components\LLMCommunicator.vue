<template>
  <div class="llm-communicator">
    <div class="llm-header">
      <div class="llm-title">
        {{ title }}
      </div>
      <div
        v-if="showAdvanced"
        class="toggle-prompt"
      >
        <button
          class="toggle-button"
          @click="showPromptEditor = !showPromptEditor"
        >
          {{ showPromptEditor ? '收起提示词' : '展开提示词' }}
        </button>
      </div>
    </div>
    
    <!-- 输入区域 -->
    <div class="input-section">
      <div class="section-title">
        输入文本
      </div>
      <textarea
        v-model="inputText"
        class="text-input"
        :placeholder="placeholder || '请输入需要分析的文本...'"
        :disabled="isLoading"
        :rows="textareaRows"
      />
      
      <!-- 提示词自定义区域 -->
      <div
        v-if="showPromptEditor"
        class="prompt-editor"
      >
        <div class="prompt-header">
          <div class="prompt-title">
            自定义提示词
          </div>
          <div class="prompt-controls">
            <button
              class="prompt-control-button"
              @click="resetPrompt"
            >
              <i class="ri-refresh-line" /> 重置
            </button>
            <button
              class="prompt-control-button"
              @click="previewPrompt"
            >
              <i class="ri-eye-line" /> 预览
            </button>
          </div>
        </div>
        
        <textarea
          v-model="customPrompt"
          class="prompt-textarea"
          placeholder="输入自定义提示词..."
          rows="4"
        />
        
        <div class="prompt-info">
          <div class="info-item">
            <i class="ri-information-line" />
            <span>提示：修改提示词可以让AI更好地理解您的需求。</span>
          </div>
          <div class="info-item">
            <i class="ri-file-list-line" />
            <span>使用 <code>{text}</code> 表示您输入的文本</span>
          </div>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="action-buttons">
        <button 
          class="action-button" 
          @click="clearInput" 
          :disabled="isLoading || !inputText"
          title="清空输入"
        >
          <i class="ri-delete-bin-line" />
          清空
        </button>
        
        <slot name="additional-buttons" />
        
        <button 
          class="action-button primary" 
          @click="processText" 
          :disabled="isLoading || !inputText"
          title="开始分析"
        >
          <i class="ri-brain-line" />
          {{ isLoading ? '处理中...' : buttonText || '开始分析' }}
        </button>
      </div>
    </div>
    
    <!-- 加载状态指示器 -->
    <div
      v-if="isLoading"
      class="loading-indicator"
    >
      <div class="spinner" />
      <div class="loading-text">
        {{ statusMessage || '正在处理文本，请稍候...' }}
      </div>
    </div>
    
    <!-- 错误提示 -->
    <div
      v-if="error"
      class="error-message"
    >
      <i class="ri-error-warning-line" />
      <span>{{ error }}</span>
    </div>
    
    <!-- 结果显示区域 -->
    <div
      v-if="responseText && !error"
      class="result-section"
    >
      <div class="section-title">
        处理结果
      </div>
      
      <!-- 格式化结果 -->
      <slot
        name="formatted-result"
        :data="responseData"
      >
        <!-- 默认显示格式 -->
        <div
          v-if="responseData"
          class="formatted-result"
        >
          <pre>{{ JSON.stringify(responseData, null, 2) }}</pre>
        </div>
      </slot>
      
      <!-- 原始文本结果 -->
      <div
        v-if="!responseData || showRawOutput"
        class="raw-output"
      >
        <div class="subsection-title">
          {{ responseData ? '原始输出' : '文本结果' }}
        </div>
        <pre>{{ responseText }}</pre>
      </div>
      
      <!-- 结果操作按钮 -->
      <div
        v-if="responseText"
        class="result-actions"
      >
        <button
          class="action-button"
          @click="copyResultToClipboard"
          title="复制结果到剪贴板"
        >
          <i class="ri-clipboard-line" />
          复制结果
        </button>
        
        <slot name="result-actions" />
      </div>
    </div>
    
    <!-- 提示词预览弹窗 -->
    <BaseModal
      v-if="baseModalAvailable"
      :show="showPromptPreview"
      @update:show="showPromptPreview = false"
      title="完整提示词预览"
    >
      <div class="preview-content">
        <pre>{{ fullPromptPreview }}</pre>
      </div>
      <template #footer>
        <div class="modal-footer">
          <button
            class="button primary"
            @click="showPromptPreview = false"
          >
            关闭
          </button>
        </div>
      </template>
    </BaseModal>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue';
import { useLLMService } from '../composables/useLLMService';

// 尝试导入BaseModal组件
let BaseModal = null;
try {
  BaseModal = require('./BaseModal.vue').default;
} catch (err) {
  console.warn('BaseModal组件不可用，提示词预览功能将不会使用弹窗');
}

export default {
  name: 'LLMCommunicator',
  components: {
    BaseModal: BaseModal
  },
  props: {
    // 基本配置
    title: {
      type: String,
      default: 'AI文本处理'
    },
    placeholder: {
      type: String,
      default: '请输入需要处理的文本...'
    },
    buttonText: {
      type: String,
      default: '开始处理'
    },
    initialText: {
      type: String,
      default: ''
    },
    textareaRows: {
      type: Number,
      default: 8
    },
    
    // 提示词相关
    defaultPrompt: {
      type: String,
      required: true
    },
    showAdvanced: {
      type: Boolean,
      default: false
    },
    
    // 结果显示相关
    showRawOutput: {
      type: Boolean,
      default: false
    },
    
    // 处理选项
    analyzeType: {
      type: String,
      default: 'text-analysis'
    },
    additionalOptions: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:result', 'processing-complete', 'error'],
  setup(props, { emit }) {
    // 使用LLM服务
    const { 
      isLoading, 
      error, 
      responseText, 
      responseData, 
      statusMessage,
      sendTextToLLM,
      processWithCustomPrompt
    } = useLLMService();
    
    // 输入文本
    const inputText = ref(props.initialText || '');
    
    // 提示词相关状态
    const showPromptEditor = ref(false);
    const customPrompt = ref(props.defaultPrompt);
    const showPromptPreview = ref(false);
    const fullPromptPreview = ref('');
    
    // 检查BaseModal组件是否可用
    const baseModalAvailable = computed(() => !!BaseModal);
    
    // 监听props变化
    watch(() => props.initialText, (newValue) => {
      if (newValue !== undefined && newValue !== null) {
        inputText.value = newValue;
      }
    });
    
    watch(() => props.defaultPrompt, (newValue) => {
      customPrompt.value = newValue;
    });
    
    // 当结果改变时，发出事件
    watch(responseData, (newValue) => {
      if (newValue) {
        emit('update:result', {
          data: newValue,
          text: responseText.value
        });
      }
    });
    
    watch(error, (newValue) => {
      if (newValue) {
        emit('error', newValue);
      }
    });
    
    // 当组件挂载时，如果有初始文本，进行处理
    onMounted(() => {
      if (props.initialText && props.initialText.trim() && props.processOnMount) {
        processText();
      }
    });
    
    // 清空输入
    const clearInput = () => {
      inputText.value = '';
    };
    
    // 重置提示词
    const resetPrompt = () => {
      customPrompt.value = props.defaultPrompt;
    };
    
    // 预览完整提示词
    const previewPrompt = () => {
      const preview = customPrompt.value.replace('{text}', inputText.value || '[您的输入文本将显示在这里]');
      fullPromptPreview.value = preview;
      showPromptPreview.value = true;
    };
    
    // 处理文本
    const processText = async () => {
      if (!inputText.value || isLoading.value) return;
      
      try {
        // 处理选项
        const options = {
          analyzeType: props.analyzeType,
          ...props.additionalOptions
        };
        
        // 使用自定义提示词或默认提示词
        const prompt = showPromptEditor.value ? customPrompt.value : props.defaultPrompt;
        
        // 处理文本
        const result = await sendTextToLLM(inputText.value, prompt, options);
        
        if (result && result.success) {
          emit('processing-complete', {
            success: true,
            data: result.data,
            text: result.text,
            originalResponse: result.originalResponse
          });
        } else if (result) {
          emit('processing-complete', {
            success: false,
            error: result.error
          });
        }
      } catch (err) {
        console.error('处理文本出错:', err);
        emit('processing-complete', {
          success: false,
          error: err.message
        });
      }
    };
    
    // 复制结果到剪贴板
    const copyResultToClipboard = () => {
      let textToCopy = '';
      
      if (responseData.value) {
        textToCopy = JSON.stringify(responseData.value, null, 2);
      } else if (responseText.value) {
        textToCopy = responseText.value;
      }
      
      if (textToCopy) {
        navigator.clipboard.writeText(textToCopy)
          .then(() => {
            alert('结果已复制到剪贴板');
          })
          .catch(err => {
            console.error('复制失败:', err);
            alert('复制失败，请手动复制');
          });
      }
    };
    
    return {
      // 状态
      inputText,
      isLoading,
      error,
      responseText,
      responseData,
      statusMessage,
      
      // 提示词相关
      showPromptEditor,
      customPrompt,
      showPromptPreview,
      fullPromptPreview,
      baseModalAvailable,
      
      // 方法
      clearInput,
      resetPrompt,
      previewPrompt,
      processText,
      copyResultToClipboard
    };
  }
}
</script>

<style scoped>
.llm-communicator {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.llm-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.llm-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #e0def4;
}

.toggle-button {
  padding: 4px 8px;
  font-size: 0.8rem;
  background-color: #2a273f;
  border: 1px solid #44415a;
  border-radius: 4px;
  color: #e0def4;
  cursor: pointer;
}

.toggle-button:hover {
  background-color: #363342;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #e0def4;
  border-bottom: 1px solid #44415a;
  padding-bottom: 4px;
}

.input-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.text-input, .prompt-textarea {
  width: 100%;
  background-color: #232136;
  border: 1px solid #44415a;
  border-radius: 4px;
  color: #e0def4;
  padding: 12px;
  font-size: 0.95rem;
  resize: vertical;
  line-height: 1.5;
  font-family: inherit;
}

.text-input:focus, .prompt-textarea:focus {
  border-color: #3e8fb0;
  outline: none;
}

.prompt-editor {
  background-color: #2a273f;
  border: 1px solid #44415a;
  border-radius: 4px;
  padding: 12px;
  margin-top: 8px;
}

.prompt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.prompt-title {
  font-weight: 600;
  color: #e0def4;
  font-size: 0.9rem;
}

.prompt-controls {
  display: flex;
  gap: 8px;
}

.prompt-control-button {
  padding: 4px 8px;
  background-color: #232136;
  border: 1px solid #44415a;
  border-radius: 4px;
  color: #e0def4;
  font-size: 0.75rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
}

.prompt-control-button:hover {
  background-color: #363342;
}

.prompt-info {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 0.8rem;
  color: #908caa;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.info-item code {
  background-color: #232136;
  padding: 2px 4px;
  border-radius: 2px;
  font-family: monospace;
}

.action-buttons {
  display: flex;
  gap: 12px;
  margin-top: 8px;
}

.action-button {
  padding: 8px 12px;
  background-color: #232136;
  border: 1px solid #44415a;
  border-radius: 4px;
  color: #e0def4;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  flex: 1;
}

.action-button:hover:not(:disabled) {
  background-color: #2a273f;
  border-color: #56526e;
  transform: translateY(-2px);
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-button.primary {
  background-color: #3e8fb0;
  border-color: #3e8fb0;
  color: #232136;
  font-weight: 500;
}

.action-button.primary:hover:not(:disabled) {
  background-color: #4ea8c6;
  border-color: #4ea8c6;
}

.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin: 16px 0;
}

.spinner {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(62, 143, 176, 0.3);
  border-radius: 50%;
  border-top-color: #3e8fb0;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  color: #908caa;
  font-size: 0.9rem;
}

.error-message {
  margin: 16px 0;
  padding: 12px;
  background-color: rgba(235, 111, 146, 0.2);
  border-left: 4px solid #eb6f92;
  color: #e0def4;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.result-section {
  margin-top: 16px;
  border: 1px solid #44415a;
  border-radius: 4px;
  padding: 16px;
  background-color: #232136;
}

.subsection-title {
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #e0def4;
}

.formatted-result, .raw-output {
  background-color: #2a273f;
  border: 1px solid #44415a;
  border-radius: 4px;
  padding: 12px;
  overflow: auto;
  max-height: 400px;
  margin-bottom: 16px;
}

.formatted-result pre, .raw-output pre {
  white-space: pre-wrap;
  font-family: monospace;
  font-size: 0.9rem;
  color: #e0def4;
  line-height: 1.4;
}

.result-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.preview-content {
  max-height: 70vh;
  overflow-y: auto;
  background-color: #2a273f;
  padding: 10px;
  border-radius: 4px;
}

.preview-content pre {
  font-family: monospace;
  font-size: 0.85rem;
  color: #e0def4;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-word;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 16px;
}

.button {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;
}

.button.primary {
  background-color: #3e8fb0;
  border-color: #3e8fb0;
  color: #232136;
  font-weight: 500;
  border: none;
}

.button.primary:hover {
  background-color: #4ea8c6;
}
</style> 