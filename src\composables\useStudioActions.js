import { ref } from 'vue';

export function useStudioActions() {
  // Toast通知相关
  const toastMessage = ref('');
  const toastTitle = ref('');
  const toastType = ref('info');
  const serverBaseUrl = ref('http://localhost:8080');

  // 显示错误消息
  const showErrorMessage = (message) => {
    console.error(message);
    toastMessage.value = message;
    toastTitle.value = '错误';
    toastType.value = 'error';
    return { toastMessage, toastTitle, toastType };
  };

  // 显示成功消息
  const showSuccessMessage = (title, message) => {
    toastMessage.value = message;
    toastTitle.value = title;
    toastType.value = 'success';
    return { toastMessage, toastTitle, toastType };
  };

  // 显示信息消息
  const showInfoMessage = (title, message) => {
    toastMessage.value = message;
    toastTitle.value = title;
    toastType.value = 'info';
    return { toastMessage, toastTitle, toastType };
  };

  // 获取服务器基础URL
  const getServerBaseUrl = async () => {
    try {
      // 不再尝试连接多个端口，直接使用当前应用运行的端口
      serverBaseUrl.value = 'http://localhost:8080';
      console.log('使用当前应用端口作为API服务器:', serverBaseUrl.value);
      return serverBaseUrl.value;
    } catch (error) {
      console.error('初始化服务器URL出错:', error);
      return serverBaseUrl.value;
    }
  };

  // 构建API URL
  const getApiUrl = (path) => {
    return `${serverBaseUrl.value}/api/${path}`;
  };

  // 保存项目数据
  const saveProject = async (projectData) => {
    try {
      // 确保项目数据存在
      if (!projectData) {
        showErrorMessage('项目数据不存在，无法保存');
        return null;
      }

      // 记录保存开始时间
      console.log(`开始保存项目数据...`, projectData);
      const startTime = Date.now();

      // 确保行数据中的所有合并和分拆状态信息被正确保存
      if (projectData.data && projectData.data.rows) {
        // 深拷贝行数据，确保所有嵌套对象都被正确序列化
        const rowsToSave = JSON.parse(JSON.stringify(projectData.data.rows));

        // 检查每一行是否有合并状态
        for (const row of rowsToSave) {
          if (row.isMerged === undefined) {
            row.isMerged = false;
          }

          // 确保未合并的行没有合并相关的键
          if (!row.isMerged) {
            delete row.mergedRows;
            delete row.mergedRowsCount;
          }

          // 确保每行都有 originalState 属性
          if (!row.originalState) {
            row.originalState = {
              index: row.index,
              startTime: row.startTime,
              endTime: row.endTime,
              duration: row.duration,
              description: row.description,
              mergedWith: []
            };
          }
        }

        // 更新项目数据中的行
        projectData.data.rows = rowsToSave;

        // 添加调试日志
        console.log('保存前检查合并行数量:', rowsToSave.filter(row => row.isMerged).length);
        if (rowsToSave.filter(row => row.isMerged).length > 0) {
          // 过滤掉已删除 mergedRows 的行再记录日志
          const mergedRowsDetails = rowsToSave.filter(row => row.isMerged && row.mergedRows);
          console.log('合并行详情:', JSON.stringify(mergedRowsDetails.map(row => ({
            index: row.index,
            isMerged: row.isMerged,
            mergedRowsCount: row.mergedRows.length // 此时 mergedRows 必然存在
          }))));
        }
      }

      // 确保行数据中的所有合并和分拆状态信息被正确保存
      if (projectData.data && projectData.data.rows) {
        // 深拷贝行数据，确保所有嵌套对象都被正确序列化
        const rowsToSave = JSON.parse(JSON.stringify(projectData.data.rows));

        // 检查每一行是否有合并状态
        for (const row of rowsToSave) {
          if (row.isMerged === undefined) {
            row.isMerged = false;
          }

          // 确保未合并的行没有合并相关的键
          if (!row.isMerged) {
            delete row.mergedRows;
            delete row.mergedRowsCount;
          }

          // 确保每行都有 originalState 属性
          if (!row.originalState) {
            row.originalState = {
              index: row.index,
              startTime: row.startTime,
              endTime: row.endTime,
              duration: row.duration,
              description: row.description,
              mergedWith: []
            };
          }
        }

        // 更新项目数据中的行
        projectData.data.rows = rowsToSave;

        // 添加调试日志
        console.log('保存前检查合并行数量:', rowsToSave.filter(row => row.isMerged).length);
        if (rowsToSave.filter(row => row.isMerged).length > 0) {
          // 过滤掉已删除 mergedRows 的行再记录日志
          const mergedRowsDetails = rowsToSave.filter(row => row.isMerged && row.mergedRows);
          console.log('合并行详情:', JSON.stringify(mergedRowsDetails.map(row => ({
            index: row.index,
            isMerged: row.isMerged,
            mergedRowsCount: row.mergedRows.length // 此时 mergedRows 必然存在
          }))));
        }
      }

      // 构建保存请求
      const saveData = {
        projectTitle: projectData.title || '',
        chapterTitle: projectData.currentChapter || '',
        data: projectData.data || {}
      };

      // 保存到API
      const response = await fetch('/api/local/save-project-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(saveData)
      });

      if (!response.ok) {
        throw new Error('保存项目失败');
      }

      const result = await response.json();
      const endTime = Date.now();

      console.log(`项目保存成功，耗时: ${endTime - startTime}ms`);

      // 显示成功消息
      showSuccessMessage('保存成功', '项目数据已成功保存');

      return result;
    } catch (error) {
      console.error('保存项目失败:', error);
      showErrorMessage('保存项目失败: ' + error.message);
      return null;
    }
  };

  // 执行智能推理
  const performSmartReasoning = () => {
    showInfoMessage('功能提示', '智能推理功能执行中...');
  };

  // 生成配图
  const generateImages = () => {
    showInfoMessage('功能提示', '生成配图功能执行中...');
  };

  // 放大配图
  const enlargeImages = () => {
    showInfoMessage('功能提示', '放大配图功能执行中...');
  };

  /**
   * 向上合并字幕行 - 完全重构版
   * @param {Number} index 当前行索引
   * @param {Array} rows 所有字幕行数据
   * @returns {Object} 处理结果，包含更新后的行数据
   */
  const mergeUp = (index, rows) => {
    // 不能合并第一行
    if (index <= 0 || !rows || rows.length < 2) {
      showErrorMessage('无法合并第一行或数据不完整');
      return { success: false, rows };
    }

    try {
      // 创建行数据的深拷贝
      const newRows = JSON.parse(JSON.stringify(rows));
      
      // 获取当前行和上一行
      const currentRow = newRows[index];
      const prevRow = newRows[index - 1];
      
      // 1. 确保两行都有 originalIndex
      if (!currentRow.originalIndex) {
        currentRow.originalIndex = currentRow.index || index;
      }
      
      if (!prevRow.originalIndex) {
        prevRow.originalIndex = prevRow.index || (index - 1);
      }
      
      // 2. 如果上一行不是合并行，保存其原始状态
      if (!prevRow.isMerged) {
        prevRow.originalState = {
          originalIndex: prevRow.originalIndex,
          index: prevRow.index,
          description: prevRow.description,
          startTime: prevRow.startTime,
          endTime: prevRow.endTime,
          duration: prevRow.duration
        };
        
        // 初始化 mergedRows 数组并添加自身原始数据
        prevRow.mergedRows = [{
          originalIndex: prevRow.originalIndex,
          index: prevRow.index,
          description: prevRow.description,
          startTime: prevRow.startTime,
          endTime: prevRow.endTime,
          duration: prevRow.duration,
          tags: prevRow.tags || [],
          keywords: prevRow.keywords || '',
          imageSrc: prevRow.imageSrc || '',
          imageAlt: prevRow.imageAlt || '',
          isImageLocked: prevRow.isImageLocked || false,
          thumbnails: Array.isArray(prevRow.thumbnails) ? [...prevRow.thumbnails] : []
        }];
      }
      
      // 3. 添加当前行到上一行的 mergedRows
      if (currentRow.isMerged && currentRow.mergedRows && currentRow.mergedRows.length > 0) {
        // 如果当前行是合并行，添加其所有 mergedRows
        const rowsToAdd = currentRow.mergedRows.map(subRow => ({
          originalIndex: subRow.originalIndex,
          index: subRow.index,
          description: subRow.description,
          startTime: subRow.startTime,
          endTime: subRow.endTime,
          duration: subRow.duration,
          tags: subRow.tags || [],
          keywords: subRow.keywords || '',
          imageSrc: subRow.imageSrc || '',
          imageAlt: subRow.imageAlt || '',
          isImageLocked: subRow.isImageLocked || false,
          thumbnails: Array.isArray(subRow.thumbnails) ? [...subRow.thumbnails] : []
        }));
        prevRow.mergedRows = prevRow.mergedRows.concat(rowsToAdd);
      } else {
        // 否则，添加当前行本身
        prevRow.mergedRows.push({
          originalIndex: currentRow.originalIndex,
          index: currentRow.index,
          description: currentRow.description,
          startTime: currentRow.startTime,
          endTime: currentRow.endTime,
          duration: currentRow.duration,
          tags: currentRow.tags || [],
          keywords: currentRow.keywords || '',
          imageSrc: currentRow.imageSrc || '',
          imageAlt: currentRow.imageAlt || '',
          isImageLocked: currentRow.isImageLocked || false,
          thumbnails: Array.isArray(currentRow.thumbnails) ? [...currentRow.thumbnails] : []
        });
      }
      
      // 4. 按原始索引排序合并项
      prevRow.mergedRows.sort((a, b) => 
        (a.originalIndex !== undefined && b.originalIndex !== undefined) 
          ? a.originalIndex - b.originalIndex 
          : (a.index || 0) - (b.index || 0)
      );
      
      // 5. 设置合并标记
      prevRow.isMerged = true;
      prevRow.mergedRowsCount = prevRow.mergedRows.length;
      
      // 6. 更新合并后行的显示内容 (重点修复部分)
      let combinedDescription = '';
      
      // 循环处理所有合并项的描述文本
      for (let i = 0; i < prevRow.mergedRows.length; i++) {
        if (i > 0) combinedDescription += '\n';
        combinedDescription += prevRow.mergedRows[i].description;
      }
      
      // 更新描述文本
      prevRow.description = combinedDescription;
      
      // 7. 更新时间范围（如果需要）
      if (prevRow.mergedRows.length > 0) {
        // 找到最早的开始时间和最晚的结束时间
        const startTimes = prevRow.mergedRows.map(row => row.startTime);
        const endTimes = prevRow.mergedRows.map(row => row.endTime);
        
        prevRow.startTime = Math.min(...startTimes);
        prevRow.endTime = Math.max(...endTimes);
        prevRow.duration = prevRow.endTime - prevRow.startTime;
      }
      
      // 8. 从数组中移除当前行
      newRows.splice(index, 1);
      
      // 9. 重新编号所有行
      for (let i = 0; i < newRows.length; i++) {
        newRows[i].index = i + 1;
      }
      
      // 添加调试日志
      console.log('合并完成，行数从', rows.length, '减少到', newRows.length);
      console.log('合并后行的信息:', {
        index: prevRow.index,
        isMerged: prevRow.isMerged,
        mergedRowsCount: prevRow.mergedRowsCount,
        description: prevRow.description?.substring(0, 20) + '...'
      });
      
      return { success: true, rows: newRows };
    } catch (error) {
      console.error('合并行时出错:', error);
      showErrorMessage('合并失败: ' + error.message);
      return { success: false, rows };
    }
  };

  /**
   * 向下拆分已合并的字幕行 - 重新实现以提高可靠性
   * @param {Number} index 当前行索引
   * @param {Array} rows 所有字幕行数据
   * @returns {Object} 处理结果，包含更新后的行数据
   */
  const splitDown = (index, rows) => {
    if (index < 0 || !rows || rows.length === 0) {
      showErrorMessage('无效的索引或数据不完整');
      return { success: false, rows };
    }

    try {
      // 创建行数据的深拷贝
      const newRows = JSON.parse(JSON.stringify(rows));
      const currentRow = newRows[index];

      // 1. 检查是否是合并行
      if (!currentRow.isMerged || !currentRow.mergedRows || currentRow.mergedRows.length <= 1) {
        console.log('此行不是合并行或没有足够的合并项可分拆');
        return { success: false, rows: newRows };
      }

      // 2. 将合并行恢复为第一个子项
      // 确保mergedRows已经按原始索引排序
      currentRow.mergedRows.sort((a, b) => 
        (a.originalIndex !== undefined && b.originalIndex !== undefined) 
          ? a.originalIndex - b.originalIndex 
          : (a.index || 0) - (b.index || 0)
      );
      
      const firstItem = currentRow.mergedRows[0];
      const itemsToRestore = currentRow.mergedRows.slice(1);

      // 3. 使用 originalState 恢复当前行的原始状态
      if (currentRow.originalState) {
        console.log('[SplitDown] 恢复原始状态:', currentRow.originalState);
        currentRow.description = currentRow.originalState.description;
        currentRow.originalIndex = currentRow.originalState.originalIndex;
        currentRow.startTime = currentRow.originalState.startTime;
        currentRow.endTime = currentRow.originalState.endTime;
        currentRow.duration = currentRow.originalState.duration;
        // 保留原始状态中的其他可能属性，但移除合并相关的
        const originalStateCopy = { ...currentRow.originalState };
        delete originalStateCopy.mergedWith; // mergedWith 可能在原始状态中
        currentRow.originalState = originalStateCopy; // 更新为清理后的原始状态
      } else {
        // 如果没有 originalState，尝试用 firstItem 作为后备，但这不理想
        console.warn('[SplitDown] 警告：未找到currentRow的originalState，尝试使用第一个合并项恢复。');
        currentRow.description = firstItem.description;
        currentRow.originalIndex = firstItem.originalIndex;
        currentRow.startTime = firstItem.startTime;
        currentRow.endTime = firstItem.endTime;
        currentRow.duration = firstItem.duration;
        // 创建一个基本的 originalState
        currentRow.originalState = {
          originalIndex: firstItem.originalIndex,
          index: firstItem.originalIndex,
          startTime: firstItem.startTime,
          endTime: firstItem.endTime,
          duration: firstItem.duration,
          description: firstItem.description
        };
      }

      // 清除合并状态
      currentRow.isMerged = false;
      delete currentRow.mergedRows;
      delete currentRow.mergedRowsCount; // 显式删除 mergedRowsCount

      // 4. 将其余项转换为完整行并插入到当前行之后
      const rowsToInsert = itemsToRestore.map(item => ({
        originalIndex: item.originalIndex,
        index: item.originalIndex,
        description: item.description,
        startTime: item.startTime,
        endTime: item.endTime,
        duration: item.duration,
        isMerged: false,
        originalState: {
          originalIndex: item.originalIndex,
          index: item.originalIndex,
          startTime: item.startTime,
          endTime: item.endTime,
          duration: item.duration,
          description: item.description
        },
        // 复制其他必要字段
        tags: item.tags || [],
        keywords: item.keywords || '',
        imageSrc: item.imageSrc || '',
        imageAlt: item.imageAlt || '',
        isImageLocked: item.isImageLocked || false,
        thumbnails: item.thumbnails || [],
        isSelected: false
      }));

      // 按原始索引排序
      rowsToInsert.sort((a, b) => 
        (a.originalIndex !== undefined && b.originalIndex !== undefined) 
          ? a.originalIndex - b.originalIndex 
          : (a.index || 0) - (b.index || 0)
      );

      // 插入新行到当前行之后
      newRows.splice(index + 1, 0, ...rowsToInsert);

      // 5. 重新编号所有行
      for (let i = 0; i < newRows.length; i++) {
        newRows[i].index = i + 1;
      }

      // 添加调试日志
      console.log('分拆完成，行数从', rows.length, '增加到', newRows.length);
      console.log('恢复的行数量:', rowsToInsert.length);
      console.log('首个恢复项:', {
        originalIndex: firstItem.originalIndex,
        index: currentRow.index,
        description: currentRow.description?.substring(0, 20) + '...'
      });

      return { success: true, rows: newRows };
    } catch (error) {
      console.error('分拆行时出错:', error);
      showErrorMessage('分拆失败: ' + error.message);
      return { success: false, rows };
    }
  };

  /**
   * 将合并后的行恢复到原始SRT状态
   * @param {Array} rows 当前行数据
   * @returns {Array} 恢复到原始状态的行数据
   */
  // eslint-disable-next-line no-unused-vars
  const restoreToOriginalSrt = (rows) => {
    if (!rows || rows.length === 0) {
      return [];
    }

    try {
      // 创建一个新的行数组
      let restoredRows = [];

      // 遍历所有行
      for (const row of rows) {
        // 如果行没有被合并，直接添加
        if (!row.isMerged) {
          restoredRows.push({...row});
          continue;
        }

        // 如果行被合并了，先添加原始行
        if (row.originalState) {
          const originalRow = {
            ...row,
            description: row.originalState.description,
            startTime: row.originalState.startTime,
            endTime: row.originalState.endTime,
            duration: row.originalState.duration,
            isMerged: false
          };

          restoredRows.push(originalRow);

          // 然后添加所有被合并的行
          if (row.mergedRows && row.mergedRows.length > 0) {
            for (const mergedRow of row.mergedRows) {
              restoredRows.push({
                ...mergedRow,
                isSelected: false,
                isMerged: false
              });
            }
          }
        }
      }

      // 按照原始索引排序
      restoredRows.sort((a, b) => a.index - b.index);

      return restoredRows;
    } catch (error) {
      console.error('恢复原始SRT状态时出错:', error);
      showErrorMessage('恢复原始SRT状态失败: ' + error.message);
      return rows;
    }
  };

  // 图片操作
  // 选择主图
  const selectMainImage = () => {
    showInfoMessage('功能提示', '请选择或上传图片...');
  };

  // 切换图片锁定状态
  const toggleImageLock = (isImageLocked) => {
    const newLockState = !isImageLocked;
    showInfoMessage('图片状态', newLockState ? '图片已锁定' : '图片已解锁');
    return newLockState;
  };

  // 清空主图
  const clearMainImage = () => {
    showSuccessMessage('操作成功', '已清空当前图片');
    return '';
  };

  // 选择缩略图
  const selectThumbnail = (index) => {
    showInfoMessage('功能提示', `已选择缩略图 ${index + 1}`);
  };

  // 管理图片
  const manageImages = () => {
    showInfoMessage('功能提示', '打开图片管理面板...');
  };

  // 重绘图片
  const redrawImage = () => {
    showInfoMessage('重绘中', '正在重新生成当前镜头图片...');
  };

  // 推理提示词
  const inferPrompt = () => {
    showInfoMessage('推理中', '正在根据原文智能推理提示词...');
  };

  /**
   * 检查章节标题是否重名
   * @param {Array} chapters 章节数组
   * @param {String} title 待检测标题
   * @param {String} [excludeId] 排除的章节ID（编辑时用）
   * @returns {Boolean}
   */
  const isChapterTitleDuplicate = (chapters, title, excludeId = null) => {
    return chapters.some(c => c.title === title && (!excludeId || c.id !== excludeId));
  };

  /**
   * 生成下一个默认章节标题（如 CH1, CH2 ...）
   * @param {Array} chapters 章节数组
   * @returns {String}
   */
  const getNextChapterTitle = (chapters) => {
    let nextNum = chapters.length + 1;
    let defaultTitle = `CH${nextNum}`;
    while (chapters.some(c => c.title === defaultTitle)) {
      nextNum++;
      defaultTitle = `CH${nextNum}`;
    }
    return defaultTitle;
  };

  /**
   * 创建新章节（含后端目录创建）
   * @param {Object} params { projectId, projectTitle, chapters, getApiUrl }
   * @returns {Promise<Object>} 新章节对象
   */
  const createChapter = async ({ projectId, projectTitle, chapters, getApiUrl }) => {
    const title = getNextChapterTitle(chapters);
    console.log('[createChapter] 创建章节', { projectId, projectTitle, title });
    // 创建章节目录
    const createDirResponse = await fetch(getApiUrl('createChapter'), {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ projectId, projectTitle, chapterTitle: title })
    });
    console.log('[createChapter] 目录创建响应', createDirResponse);
    if (!createDirResponse.ok) {
      const errorData = await createDirResponse.json().catch(() => ({}));
      console.error('[createChapter] 创建章节目录失败', errorData);
      throw new Error(errorData.error || '创建章节目录失败');
    }
    // 创建章节数据
    const chapterObj = {
      id: `chapter_${Date.now()}`,
      title,
      createdAt: new Date().toISOString(),
      status: 'draft',
      data: {}
    };
    console.log('[createChapter] 创建章节成功', chapterObj);
    return chapterObj;
  };

  /**
   * 重命名章节（含后端同步）
   * @param {Object} params { novelData, chapters, chapterId, newTitle, getApiUrl }
   * @returns {Promise<Object>} 更新后的 novelData
   */
  const renameChapter = async ({ novelData, chapters, chapterId, newTitle, getApiUrl }) => {
    const chapterIndex = chapters.findIndex(c => c.id === chapterId);
    if (chapterIndex === -1) throw new Error('章节未找到');

    const oldTitle = chapters[chapterIndex].title;
    console.log('[renameChapter] 重命名章节', { chapterId, oldTitle, newTitle });

    // 如果标题没有变化，直接返回
    if (oldTitle === newTitle) {
      console.log('[renameChapter] 标题未变化，跳过重命名');
      return novelData;
    }

    // 更新本地数据
    chapters[chapterIndex].title = newTitle;

    // 深拷贝章节，移除data中的循环引用
    const safeChapters = chapters.map(ch => {
      // 只保留必要字段，且data字段只保留纯数据
      const { id, title, createdAt, updatedAt, status, data } = ch;
      // 移除data中可能的循环引用
      let safeData = {};
      if (data && typeof data === 'object') {
        // 只保留data中的原始数据，不带novelData/chapters等
        const { ...rest } = data;
        safeData = rest;
      }
      return { id, title, createdAt, updatedAt, status, data: safeData };
    });

    // 构造更新数据
    const updatedNovel = {
      ...novelData,
      updatedAt: new Date().toISOString(),
      data: {
        ...novelData.data,
        chapters: safeChapters,
        renameChapterInfo: {
          chapterId,
          oldTitle,
          newTitle
        },
        renameChapterFolder: true,
        forceUnique: true
      }
    };

    console.log('[renameChapter] PUT 请求参数', updatedNovel);

    // 发送请求到服务器
    const response = await fetch(getApiUrl(`projects/${novelData.id}`), {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updatedNovel)
    });

    console.log('[renameChapter] PUT 响应', response);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('[renameChapter] 章节重命名失败', errorData);
      throw new Error(errorData.error || '章节重命名失败');
    }

    const result = await response.json();
    console.log('[renameChapter] 章节重命名成功', result);

    return result;
  };

  /**
   * 通用章节创建/重命名方法
   * @param {Object} params { mode, novelData, chapters, chapterId, newTitle, projectId, projectTitle, getApiUrl }
   * @returns {Promise<Object>} 新章节对象或更新后的 novelData
   */
  const createOrRenameChapter = async (params) => {
    console.log('[createOrRenameChapter] 入参', params);
    const { mode, novelData, chapters, chapterId, newTitle, projectId, projectTitle, getApiUrl } = params;
    if (mode === 'create') {
      return await createChapter({ projectId, projectTitle, chapters, getApiUrl });
    } else if (mode === 'rename') {
      return await renameChapter({ novelData, chapters, chapterId, newTitle, getApiUrl });
    } else {
      throw new Error('未知章节操作类型');
    }
  };

  return {
    // 状态
    toastMessage,
    toastTitle,
    toastType,
    serverBaseUrl,

    // 方法
    showErrorMessage,
    showSuccessMessage,
    showInfoMessage,
    getServerBaseUrl,
    getApiUrl,
    saveProject,
    performSmartReasoning,
    generateImages,
    enlargeImages,
    mergeUp,
    splitDown,
    selectMainImage,
    toggleImageLock,
    clearMainImage,
    selectThumbnail,
    manageImages,
    redrawImage,
    inferPrompt,
    isChapterTitleDuplicate,
    getNextChapterTitle,
    createChapter,
    renameChapter,
    createOrRenameChapter
  };
}