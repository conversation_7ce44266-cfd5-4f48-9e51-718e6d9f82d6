<template>
  <div class="grid-cell keyword-cell">
    <div class="keyword-wrapper">
      <textarea 
        class="keyword-textarea" 
        placeholder=""
        v-model="localKeywords"
        @input="handleInput"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'KeywordCell',
  props: {
    keywords: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      localKeywords: this.keywords
    }
  },
  watch: {
    keywords(newVal) {
      this.localKeywords = newVal;
    }
  },
  methods: {
    handleInput() {
      this.$emit('update:keywords', this.localKeywords);
    }
  }
}
</script>

<style scoped>
.grid-cell {
  display: table-cell;
  text-align: center;
  vertical-align: top;
  border-right: 1px solid #333;
  border-bottom: 1px solid #333;
  color: #e0e0e0;
  box-sizing: border-box;
  font-size: 0.9rem;
  padding: 0;
  overflow: hidden;
}

.keyword-cell {
  width: 23.75%;
}

.keyword-wrapper {
  width: 100%;
  height: 100%;
  background-color: #252525;
  padding: 5px;
}

.keyword-textarea {
  width: 100%;
  height: 100%;
  background-color: #1a1a1a;
  border: 1px solid #333;
  border-radius: 4px;
  color: #e0e0e0;
  font-size: 0.9rem;
  padding: 8px;
  resize: none;
  outline: none;
}
</style> 