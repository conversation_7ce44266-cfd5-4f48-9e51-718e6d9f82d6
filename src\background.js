'use strict'

import { app, protocol, BrowserWindow, ipc<PERSON>ain, dialog, Menu } from 'electron'
import { createProtocol } from 'vue-cli-plugin-electron-builder/lib'
import installExtension, { VUEJS3_DEVTOOLS } from 'electron-devtools-installer'
import path from 'path'
import fs from 'fs'

const isDevelopment = process.env.NODE_ENV !== 'production'

// 数据目录和文件
const dataDir = path.join(app.getPath('userData'), 'data');
const projectsFile = path.join(dataDir, 'projects.json');
const projectsDir = path.join(dataDir, 'projects');

// 确保目录和文件存在
function ensureDataDir() {
  // 确保主数据目录存在
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
  
  // 确保项目目录存在
  if (!fs.existsSync(projectsDir)) {
    fs.mkdirSync(projectsDir, { recursive: true });
  }
  
  // 确保项目索引文件存在
  if (!fs.existsSync(projectsFile)) {
    fs.writeFileSync(projectsFile, JSON.stringify([]));
  }
}

// 确保目录存在
function ensureDir(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

// Scheme must be registered before the app is ready
protocol.registerSchemesAsPrivileged([
  { scheme: 'app', privileges: { secure: true, standard: true } }
])

// 取消顶部菜单栏
Menu.setApplicationMenu(null)

async function createWindow() {
  // Create the browser window.
  const win = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      // Use pluginOptions.nodeIntegration, leave this alone
      // See nklayman.github.io/vue-cli-plugin-electron-builder/guide/security.html#node-integration for more info
      nodeIntegration: process.env.ELECTRON_NODE_INTEGRATION,
      contextIsolation: !process.env.ELECTRON_NODE_INTEGRATION,
      preload: path.join(__dirname, 'preload.js')
    }
  })

  if (process.env.WEBPACK_DEV_SERVER_URL) {
    // Load the url of the dev server if in development mode
    await win.loadURL(process.env.WEBPACK_DEV_SERVER_URL)
    if (!process.env.IS_TEST) win.webContents.openDevTools()
  } else {
    createProtocol('app')
    // Load the index.html when not in development
    win.loadURL('app://./index.html')
  }
}

// Quit when all windows are closed.
app.on('window-all-closed', () => {
  // On macOS it is common for applications and their menu bar
  // to stay active until the user quits explicitly with Cmd + Q
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  // On macOS it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) createWindow()
})

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.on('ready', async () => {
  if (isDevelopment && !process.env.IS_TEST) {
    // Install Vue Devtools
    try {
      await installExtension(VUEJS3_DEVTOOLS)
    } catch (e) {
      console.error('Vue Devtools failed to install:', e.toString())
    }
  }
  ensureDataDir()
  createWindow()
})

// Exit cleanly on request from parent process in development mode.
if (isDevelopment) {
  if (process.platform === 'win32') {
    process.on('message', (data) => {
      if (data === 'graceful-exit') {
        app.quit()
      }
    })
  } else {
    process.on('SIGTERM', () => {
      app.quit()
    })
  }
}

// 处理文件选择对话框
ipcMain.handle('select-files', async (event, options) => {
  const { canceled, filePaths } = await dialog.showOpenDialog(options);
  return { canceled, filePaths };
});

// 处理文件读取
ipcMain.handle('read-file', async (event, filePath) => {
  return fs.promises.readFile(filePath, 'utf8');
});

// 项目相关IPC处理
ipcMain.handle('get-projects', async () => {
  try {
    // 读取项目索引文件
    if (fs.existsSync(projectsFile)) {
      const data = await fs.promises.readFile(projectsFile, 'utf8');
      const projectsIndex = JSON.parse(data);
      return projectsIndex;
    }
    
    // 如果索引文件不存在，尝试扫描项目目录
    if (fs.existsSync(projectsDir)) {
      const folders = await fs.promises.readdir(projectsDir);
      const projectsIndex = [];
      
      for (const folder of folders) {
        const projectDir = path.join(projectsDir, folder);
        const stats = await fs.promises.stat(projectDir);
        
        if (stats.isDirectory()) {
          const projectJsonPath = path.join(projectDir, 'project.json');
          
          if (fs.existsSync(projectJsonPath)) {
            try {
              const projectData = JSON.parse(await fs.promises.readFile(projectJsonPath, 'utf8'));
              
              projectsIndex.push({
                id: projectData.id,
                title: projectData.title,
                createdAt: projectData.createdAt,
                updatedAt: projectData.updatedAt,
                folderName: folder
              });
            } catch (e) {
              console.error(`读取项目文件 ${projectJsonPath} 失败:`, e);
            }
          }
        }
      }
      
      // 保存扫描结果到索引文件
      await fs.promises.writeFile(projectsFile, JSON.stringify(projectsIndex, null, 2));
      return projectsIndex;
    }
    
    // 如果都不存在，返回空数组
    return [];
  } catch (error) {
    console.error('获取项目列表失败:', error);
    throw new Error('获取项目列表失败');
  }
});

ipcMain.handle('create-project', async (event, { title, data }) => {
  try {
    // 生成项目ID和时间戳
    const projectId = Date.now().toString();
    const createdAt = new Date().toISOString();
    
    // 创建项目对象
    const newProject = {
      id: projectId,
      title: title || '未命名项目',
      createdAt,
      updatedAt: createdAt,
      data: data || {}
    };
    
    // 为项目创建目录名
    let folderName = title ? title.replace(/[\\\/\:\*\?\"\<\>\|]/g, '_') : projectId;
    
    // 确保目录名唯一
    let counter = 0;
    let finalFolderName = folderName;
    while (fs.existsSync(path.join(projectsDir, finalFolderName))) {
      counter++;
      finalFolderName = `${folderName}_${counter}`;
    }
    
    // 创建项目目录
    const projectDir = path.join(projectsDir, finalFolderName);
    ensureDir(projectDir);
    
    // 创建项目子目录
    ensureDir(path.join(projectDir, 'assets'));
    ensureDir(path.join(projectDir, 'images'));
    
    // 将项目数据保存到项目目录
    const projectJsonPath = path.join(projectDir, 'project.json');
    await fs.promises.writeFile(projectJsonPath, JSON.stringify(newProject, null, 2));
    
    // 更新项目索引
    let projectsIndex = [];
    try {
      if (fs.existsSync(projectsFile)) {
        projectsIndex = JSON.parse(await fs.promises.readFile(projectsFile, 'utf8'));
      }
    } catch (e) {
      console.error('读取项目索引失败:', e);
    }
    
    // 添加到索引
    projectsIndex.push({
      id: projectId,
      title: newProject.title,
      createdAt,
      updatedAt: createdAt,
      folderName: finalFolderName
    });
    
    // 保存更新后的索引
    await fs.promises.writeFile(projectsFile, JSON.stringify(projectsIndex, null, 2));
    
    return newProject;
  } catch (error) {
    console.error('创建项目失败:', error);
    throw new Error('创建项目失败: ' + error.message);
  }
});

ipcMain.handle('get-project', async (event, id) => {
  try {
    // 从索引中查找项目
    let indexEntry = null;
    if (fs.existsSync(projectsFile)) {
      const indexData = await fs.promises.readFile(projectsFile, 'utf8');
      const projectsIndex = JSON.parse(indexData);
      indexEntry = projectsIndex.find(p => p.id === id);
    }
    
    if (indexEntry) {
      // 从项目目录读取完整项目数据
      const projectDir = path.join(projectsDir, indexEntry.folderName);
      const projectJsonPath = path.join(projectDir, 'project.json');
      
      if (fs.existsSync(projectJsonPath)) {
        const projectData = await fs.promises.readFile(projectJsonPath, 'utf8');
        return JSON.parse(projectData);
      }
    }
    
    // 如果索引中找不到，尝试在项目目录中查找
    if (fs.existsSync(projectsDir)) {
      const folders = await fs.promises.readdir(projectsDir);
      
      for (const folder of folders) {
        const projectDir = path.join(projectsDir, folder);
        const stats = await fs.promises.stat(projectDir);
        
        if (stats.isDirectory()) {
          const projectJsonPath = path.join(projectDir, 'project.json');
          
          if (fs.existsSync(projectJsonPath)) {
            try {
              const projectData = await fs.promises.readFile(projectJsonPath, 'utf8');
              const project = JSON.parse(projectData);
              
              if (project.id === id) {
                return project;
              }
            } catch (e) {
              console.error(`读取项目文件 ${projectJsonPath} 失败:`, e);
            }
          }
        }
      }
    }
    
    throw new Error('项目不存在');
  } catch (error) {
    console.error('获取项目失败:', error);
    throw new Error(error.message);
  }
});

ipcMain.handle('update-project', async (event, { id, title, data }) => {
  try {
    // 从索引查找项目
    if (!fs.existsSync(projectsFile)) {
      throw new Error('项目索引不存在');
    }
    
    const indexData = await fs.promises.readFile(projectsFile, 'utf8');
    const projectsIndex = JSON.parse(indexData);
    const indexPos = projectsIndex.findIndex(p => p.id === id);
    
    if (indexPos === -1) {
      throw new Error('项目不存在');
    }
    
    const indexEntry = projectsIndex[indexPos];
    const projectDir = path.join(projectsDir, indexEntry.folderName);
    const projectJsonPath = path.join(projectDir, 'project.json');
    
    // 读取当前项目数据
    if (!fs.existsSync(projectJsonPath)) {
      throw new Error('项目文件不存在');
    }
    
    const projectData = await fs.promises.readFile(projectJsonPath, 'utf8');
    const currentProject = JSON.parse(projectData);
    
    // 更新项目数据
    const updatedProject = {
      ...currentProject,
      title: title || currentProject.title,
      data: data || currentProject.data,
      updatedAt: new Date().toISOString()
    };
    
    // 如果标题变更，需要更新目录名
    if (title && title !== currentProject.title) {
      // 清理新标题
      const newFolderName = title.replace(/[\\\/\:\*\?\"\<\>\|]/g, '_');
      
      // 确保目录名唯一
      let counter = 0;
      let finalFolderName = newFolderName;
      while (fs.existsSync(path.join(projectsDir, finalFolderName)) && finalFolderName !== indexEntry.folderName) {
        counter++;
        finalFolderName = `${newFolderName}_${counter}`;
      }
      
      // 如果目录名变更
      if (finalFolderName !== indexEntry.folderName) {
        const newProjectDir = path.join(projectsDir, finalFolderName);
        
        // 重命名目录
        fs.renameSync(projectDir, newProjectDir);
        
        // 更新索引
        projectsIndex[indexPos].folderName = finalFolderName;
        projectsIndex[indexPos].title = title;
        projectsIndex[indexPos].updatedAt = updatedProject.updatedAt;
        
        // 保存更新后的项目数据到新目录
        const newProjectJsonPath = path.join(newProjectDir, 'project.json');
        await fs.promises.writeFile(newProjectJsonPath, JSON.stringify(updatedProject, null, 2));
      } else {
        // 保存项目数据到当前目录
        await fs.promises.writeFile(projectJsonPath, JSON.stringify(updatedProject, null, 2));
      }
    } else {
      // 保存项目数据到当前目录
      await fs.promises.writeFile(projectJsonPath, JSON.stringify(updatedProject, null, 2));
    }
    
    // 更新项目索引
    projectsIndex[indexPos].title = updatedProject.title;
    projectsIndex[indexPos].updatedAt = updatedProject.updatedAt;
    await fs.promises.writeFile(projectsFile, JSON.stringify(projectsIndex, null, 2));
    
    return updatedProject;
  } catch (error) {
    console.error('更新项目失败:', error);
    throw new Error('更新项目失败: ' + error.message);
  }
});

ipcMain.handle('delete-project', async (event, id) => {
  try {
    // 查找项目
    if (!fs.existsSync(projectsFile)) {
      throw new Error('项目索引不存在');
    }
    
    const indexData = await fs.promises.readFile(projectsFile, 'utf8');
    const projectsIndex = JSON.parse(indexData);
    const indexPos = projectsIndex.findIndex(p => p.id === id);
    
    if (indexPos === -1) {
      throw new Error('项目不存在');
    }
    
    // 删除项目目录
    const folderName = projectsIndex[indexPos].folderName;
    const projectDir = path.join(projectsDir, folderName);
    
    if (fs.existsSync(projectDir)) {
      // 递归删除目录
      const rimraf = (dir) => {
        if (fs.existsSync(dir)) {
          fs.readdirSync(dir).forEach((file) => {
            const curPath = path.join(dir, file);
            if (fs.lstatSync(curPath).isDirectory()) {
              rimraf(curPath);
            } else {
              fs.unlinkSync(curPath);
            }
          });
          fs.rmdirSync(dir);
        }
      };
      
      rimraf(projectDir);
    }
    
    // 从索引中删除
    projectsIndex.splice(indexPos, 1);
    await fs.promises.writeFile(projectsFile, JSON.stringify(projectsIndex, null, 2));
    
    return true;
  } catch (error) {
    console.error('删除项目失败:', error);
    throw new Error('删除项目失败: ' + error.message);
  }
});
