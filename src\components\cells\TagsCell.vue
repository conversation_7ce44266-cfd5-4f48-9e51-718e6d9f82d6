<template>
  <div class="grid-cell tags-cell">
    <div class="tags-wrapper">
      <div class="tags-placeholder">
        <!-- 暂无标签功能，将来实现 -->
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TagsCell',
  props: {
    tags: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style scoped>
.grid-cell {
  display: table-cell;
  text-align: center;
  vertical-align: top;
  border-right: 1px solid #333;
  border-bottom: 1px solid #333;
  color: #e0e0e0;
  box-sizing: border-box;
  font-size: 0.9rem;
  padding: 0;
  overflow: hidden;
}

.tags-cell {
  width: 11.25%;
}

.tags-wrapper {
  width: 100%;
  height: 100%;
  padding: 8px;
  overflow: auto;
  background-color: #252525;
}

.tags-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #555;
  font-style: italic;
  font-size: 0.8rem;
}
</style>



 




 
