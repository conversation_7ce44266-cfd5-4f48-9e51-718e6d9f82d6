import { ref, computed } from 'vue';

export function usePresetManager(projectTitleRef) {
  const activeGlobalPresets = ref({ characters: [], specials: [] });
  const globalPresetsLoading = ref(false);
  const globalPresetsError = ref(null);

  const presetImportLoading = ref(false);
  const presetImportMessage = ref('');
  const presetImportMessageType = ref('');
  
  const showClassifyModal = ref(false);
  const linesForClassification = ref([]);
  const originallyParsedData = ref({ characters: [], specials: [] });

  const saveGlobalJson = async (fileName, jsonContent) => {
    if (!projectTitleRef.value || !fileName) {
      presetImportMessage.value = '错误: 项目标题或文件名缺失，无法保存。';
      presetImportMessageType.value = 'error';
      throw new Error('项目标题或文件名缺失');
    }
    // presetImportMessage.value = `正在保存 ${fileName}...`; // Handled by caller or UI component
    // presetImportMessageType.value = 'info';

    try {
      const response = await fetch('/api/local/save-project-file', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ projectTitle: projectTitleRef.value, fileName, content: jsonContent })
      });
      const result = await response.json();
      if (!response.ok || !result.success) {
        throw new Error(result.error || `保存 ${fileName} 未知错误`);
      }
      // presetImportMessage.value = `${fileName} 已成功保存！`;
      // presetImportMessageType.value = 'success';
      // setTimeout(() => {
      //   if (presetImportMessage.value === `${fileName} 已成功保存！`) {
      //     presetImportMessage.value = '';
      //     presetImportMessageType.value = '';
      //   }
      // }, 3000);
      return result;
    } catch (err) {
      // presetImportMessage.value = `保存 ${fileName} 失败: ${err.message}`;
      // presetImportMessageType.value = 'error';
      console.error(`saveGlobalJson (${fileName}) error:`, err);
      throw err; // Re-throw for the caller to handle UI messages
    }
  };

  const loadGlobalPresets = async () => {
    if (!projectTitleRef.value) {
      activeGlobalPresets.value = { characters: [], specials: [] };
      globalPresetsError.value = '未提供项目标题，无法加载预设。';
      return;
    }
    globalPresetsLoading.value = true;
    globalPresetsError.value = null;
    try {
      const response = await fetch(`/api/local/read-file?path=draft/${projectTitleRef.value}/Global.json`);
      if (!response.ok) {
        if (response.status === 404) {
          console.log('Global.json not found for project:', projectTitleRef.value);
          activeGlobalPresets.value = { characters: [], specials: [] };
          // No error, just no presets
        } else {
          const errorText = await response.text();
          throw new Error(`加载 Global.json 失败: ${response.status} ${errorText}`);
        }
      } else {
        const data = await response.json();
        if (data.success && data.content) {
          const parsedContent = JSON.parse(data.content);
          activeGlobalPresets.value = {
            characters: parsedContent.characters || [],
            specials: parsedContent.specials || []
          };
        } else if (data.error === '文件不存在') {
            console.log('Global.json confirmed not found by API for project:', projectTitleRef.value);
            activeGlobalPresets.value = { characters: [], specials: [] };
        } else {
          console.warn('[usePresetManager] API call successful but data.success is false or content is missing.', data);
          activeGlobalPresets.value = { characters: [], specials: [] };
          throw new Error(data.error || '解析预设内容失败。');
        }
      }
    } catch (err) {
      console.error('[usePresetManager] Error loading or parsing Global.json:', err);
      globalPresetsError.value = err.message;
      activeGlobalPresets.value = { characters: [], specials: [] }; // Reset on error
    } finally {
      globalPresetsLoading.value = false;
    }
  };

  const parseTextToPresetItems = (content) => {
    const lines = content.split(/\r\n|\n|\r/);
    const parsed = { characters: [], specials: [] };
    const unclassified = [];

    lines.forEach(line => {
      line = line.trim();
      if (!line) return;

      let type = '';
      let name = '';
      let description = '';
      let processedLine = line;

      if (line.toLowerCase().startsWith('{character}')) {
        type = 'character';
        processedLine = line.substring('{character}'.length).trim();
      } else if (line.toLowerCase().startsWith('{sp}')) {
        type = 'special';
        processedLine = line.substring('{sp}'.length).trim();
      } else {
        unclassified.push(line);
        return;
      }

      const parts = processedLine.split('-');
      if (parts.length >= 2) {
        name = parts[0].trim();
        description = parts.slice(1).join('-').trim();
      } else {
        console.warn(`[usePresetManager] 无法解析预设文件中的标记行: ${line}`);
        unclassified.push(line); // Treat as unclassified if parsing fails
        return;
      }
      
      if (name && type === 'character') {
        parsed.characters.push({ name, description });
      } else if (name && type === 'special') {
        parsed.specials.push({ name, description });
      }
    });
    return { parsed, unclassified };
  };
  
  // Note: handlePresetFileSelected will be more complex as it involves FileReader
  // It might be better to keep FileReader in the component and pass content to a method here.
  // For now, let's assume the component handles file reading.

  const processImportedData = async (parsedData, unclassifiedLines) => {
    originallyParsedData.value = parsedData; // Store initially parsed from {character}/{sp} tags

    if (unclassifiedLines.length > 0) {
      linesForClassification.value = unclassifiedLines;
      showClassifyModal.value = true;
      // Message to be set by component: "部分条目需要分类。"
      return { needsClassification: true };
    } else {
      if (parsedData.characters.length === 0 && parsedData.specials.length === 0) {
        // Message to be set by component: "文件中未找到有效的条目..."
        return { needsClassification: false, saved: false, messageType: 'info', message: '无有效条目' };
      }
      // Directly save if no unclassified lines and some parsed data exists
      try {
        const combinedCharacters = [...(activeGlobalPresets.value.characters || [])];
        const combinedSpecials = [...(activeGlobalPresets.value.specials || [])];

        parsedData.characters.forEach(fc => {
          const idx = combinedCharacters.findIndex(c => c.name === fc.name);
          if (idx !== -1) combinedCharacters[idx] = fc; // Update existing
          else combinedCharacters.push(fc); // Add new
        });
        parsedData.specials.forEach(fs => {
          const idx = combinedSpecials.findIndex(s => s.name === fs.name);
          if (idx !== -1) combinedSpecials[idx] = fs; // Update existing
          else combinedSpecials.push(fs); // Add new
        });

        await saveGlobalJson('Global.json', { characters: combinedCharacters, specials: combinedSpecials });
        activeGlobalPresets.value = { characters: combinedCharacters, specials: combinedSpecials };
        return { needsClassification: false, saved: true, messageType: 'success', message: '预设已导入并保存' };
      } catch (err) {
        console.error("Error saving directly tagged presets from file:", err);
        throw err; // Component should handle this error message
      }
    }
  };

  const resetPresetImportStates = () => {
    presetImportLoading.value = false;
    // presetImportMessage.value = '导入操作已取消或关闭。'; // UI concern
    // presetImportMessageType.value = 'info';
    linesForClassification.value = [];
    originallyParsedData.value = { characters: [], specials: [] };
    // setTimeout for message clearing is a UI concern
  };

  const handleClassificationComplete = async (classifiedItems) => {
    // Initialize with all existing presets from activeGlobalPresets
    const charactersToSave = [...(activeGlobalPresets.value.characters || [])];
    const specialsToSave = [...(activeGlobalPresets.value.specials || [])];

    // First, merge items that were originally parsed with tags from the current import file
    // This ensures they are also candidates for being updated by classifiedItems if names match,
    // or are added if they don't conflict.
    (originallyParsedData.value.characters || []).forEach(origChar => {
      const idx = charactersToSave.findIndex(c => c.name === origChar.name);
      if (idx !== -1) {
        charactersToSave[idx] = { ...charactersToSave[idx], ...origChar }; // Update existing from original parse
      } else {
        charactersToSave.push(origChar); // Add new from original parse
      }
    });

    (originallyParsedData.value.specials || []).forEach(origSpecial => {
      const idx = specialsToSave.findIndex(s => s.name === origSpecial.name);
      if (idx !== -1) {
        specialsToSave[idx] = { ...specialsToSave[idx], ...origSpecial }; // Update existing from original parse
      } else {
        specialsToSave.push(origSpecial); // Add new from original parse
      }
    });

    // Then, merge the items manually classified in the modal
    // These will overwrite/add to what's now in charactersToSave/specialsToSave
    // (which includes existing global presets + originally parsed items from current file)
    classifiedItems.forEach(item => {
      if (item.parseError) return;
      // Check if item already exists (could be from global or originallyParsed), if so, it's an update from classification
      // If not, it's a new item from classification
      if (item.type === 'character') {
        const charData = { name: item.name, description: item.description };
        const existingCharIndex = charactersToSave.findIndex(c => c.name === item.name);
        if (existingCharIndex !== -1) charactersToSave[existingCharIndex] = charData;
        else charactersToSave.push(charData);
      } else if (item.type === 'special') {
        const specialData = { name: item.name, description: item.description };
        const existingSpecialIndex = specialsToSave.findIndex(s => s.name === item.name);
        if (existingSpecialIndex !== -1) specialsToSave[existingSpecialIndex] = specialData;
        else specialsToSave.push(specialData);
      }
    });
    
    try {
      if (!projectTitleRef.value || projectTitleRef.value.trim() === '') {
        throw new Error('项目标题未提供，无法保存。');
      }
      const dataToSave = { characters: charactersToSave, specials: specialsToSave };
      await saveGlobalJson('Global.json', dataToSave);
      activeGlobalPresets.value = { ...dataToSave };
      showClassifyModal.value = false;
      resetPresetImportStates();
      return { saved: true, messageType: 'success', message: '分类预设已保存' };
    } catch (err) {
      console.error("Error saving combined presets in handleClassificationComplete:", err);
      showClassifyModal.value = false; // Ensure modal closes even on error
      resetPresetImportStates();
      throw err; // Component handles UI message
    }
  };
  
  const handleClassificationCancel = () => {
    showClassifyModal.value = false;
    resetPresetImportStates();
    // Return a status or message for the component if needed
    return { cancelled: true, messageType: 'info', message: '分类已取消' };
  };

  const handleClassifyModalUpdateShow = (value) => {
    showClassifyModal.value = value;
    if (!value) { // If modal is closed externally
      resetPresetImportStates();
    }
  };

  const deletePresetItem = async (itemToDelete) => {
    const { characters, specials } = activeGlobalPresets.value;
    let updated = false;
    let newCharacters = characters.filter(c => !(itemToDelete.type === '角色' && c.name === itemToDelete.name));
    let newSpecials = specials.filter(s => !(itemToDelete.type === '物品' && s.name === itemToDelete.name));

    if (newCharacters.length !== characters.length || newSpecials.length !== specials.length) {
      updated = true;
    }

    if (updated) {
      try {
        const dataToSave = { characters: newCharacters, specials: newSpecials };
        await saveGlobalJson('Global.json', dataToSave);
        activeGlobalPresets.value = { ...dataToSave };
        return { deleted: true, messageType: 'success', message: `"${itemToDelete.name}" 已删除` };
      } catch (err) {
        console.error("Error deleting preset item:", err);
        throw err; // Component handles UI message
      }
    } else {
      return { deleted: false, messageType: 'info', message: `未找到条目 "${itemToDelete.name}"` };
    }
  };

  const displayedPresetItems = computed(() => {
    const items = [];
    (activeGlobalPresets.value.characters || []).forEach(char => {
      items.push({ 
        type: '角色', 
        name: char.name, 
        description: char.description || char.visualTraits || '',
        id: `char-${char.name}-${Math.random().toString(36).substring(2,7)}` // Added randomness for key
      });
    });
    (activeGlobalPresets.value.specials || []).forEach(sp => {
      items.push({ 
        type: '物品', 
        name: sp.name, 
        description: sp.description || sp.visualEffect || '',
        id: `sp-${sp.name}-${Math.random().toString(36).substring(2,7)}` // Added randomness for key
      });
    });
    return items;
  });

  const saveProjectFile = async (projectTitle, fileName, jsonContent, chapterTitle) => {
    if (!projectTitle || !fileName) {
      throw new Error('项目标题或文件名缺失');
    }
    if (!chapterTitle) {
      throw new Error('章节标题缺失');
    }

    try {
      // 构建正确的文件路径，使用正斜杠
      const filePath = `draft/${projectTitle}/${chapterTitle}/${fileName}`.replace(/\\/g, '/');
      
      const response = await fetch('/api/local/save-project-file', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          projectTitle, 
          chapterTitle,
          fileName,
          content: jsonContent,
          filePath // 使用处理后的文件路径
        })
      });
      
      const result = await response.json();
      if (!response.ok || !result.success) {
        throw new Error(result.error || `保存 ${fileName} 未知错误`);
      }
      console.log(`[usePresetManager] 文件已保存到: ${filePath}`);
      return result;
    } catch (err) {
      console.error(`saveProjectFile (${fileName}) error:`, err);
      throw err;
    }
  };

  return {
    activeGlobalPresets,
    globalPresetsLoading,
    globalPresetsError,
    presetImportLoading,
    presetImportMessage,
    presetImportMessageType,
    showClassifyModal,
    linesForClassification,
    
    loadGlobalPresets,
    parseTextToPresetItems,
    processImportedData,
    handleClassificationComplete,
    handleClassificationCancel,
    handleClassifyModalUpdateShow,
    deletePresetItem,
    displayedPresetItems,
    saveProjectFile,
    saveGlobalJson
  };
} 