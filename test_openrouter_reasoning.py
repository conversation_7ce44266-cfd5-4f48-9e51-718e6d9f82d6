import requests
import json
import os

# 直接指定 usersettings.json 路径
settings_path = os.path.join(os.getcwd(), 'userdata', 'usersettings.json')

api_key = None
if os.path.exists(settings_path):
    with open(settings_path, 'r', encoding='utf-8') as f:
        settings = json.load(f)
        api_key = settings.get('llm', {}).get('openrouterApiKey')
        if api_key:
            print(f"API Key loaded from: {settings_path}")
else:
    raise Exception(f"未能找到 {settings_path}，请检查文件路径和内容。")

if not api_key:
    raise Exception("未能自动找到 OpenRouter API Key，请检查 usersettings.json 内容。")

url = "https://openrouter.ai/api/v1/chat/completions"
headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json"
}

# 分组任务的完整 prompt
ai_grouping_prompt = '''
任务：画面感分镜分组（输出分组编号）

你需要将下方的无标点叙事文本，按照"画面感分镜"原则进行分组。每组应能独立表达一个清晰的视觉场景、动作、情绪或关键对话，适合作为AI绘画生成的画面描述。分组时请严格保留原文顺序和内容，不得增删改动任何词语。请务必了解全文内容，并根据全文内容进行分组。

分组输出要求：
- 只输出分组结果，每组用逗号分隔原始文本的行号（如"1,2,3"），每行一个分组。
- 不要输出任何解释说明或分割后的文本内容。
- 分组编号只能是1到{numberOfLines}之间的整数，且每个编号只能出现一次，不能有遗漏或重复，也不能出现超出范围的编号。
- 确保每个原始文本行只在一个分组中，且所有行都被覆盖。

分组逻辑：
1. 主要动作转变：角色开始全新动作时断开分组。
2. 叙述焦点/说话人变化：故事焦点切换时断开分组。
3. 对话单元：每句重要发言/内心独白单独成组。
4. 独立描述性片段：场景、外貌、物品等独立描述单独成组。
5. 转场短语：如"接着""然后"等，通常从这些词起新组。但不要与后文断开。
6. 反应/状态变化：角色明显反应单独成组。
7. 隐含句读：根据语义流、动作主语变化、连词等，合理断组。
8. 合并考虑：极短且紧密相关的动作可合并为一组，避免过度碎片化。
9. 清晰优先：每组都应能独立作为AI绘画提示。

举例说明（假设原始文本有8行）：
- 1,2
- 3
- 4,5,6
- 7
- 8

字幕内容（58）:
---
真阳枪
高贤送上百颗鹿角散
周烨心情好了不少
便主动指点他
真阳枪
周烨刚强调手印要稳
就目瞪口呆地看着高贤手指翻飞
瞬间结完五个手印
又快又准
再看高贤那双手
竟还挺灵巧好看
周烨心里有点不是滋味
干咳一声掩饰过去
他接着说念咒也重要
谁知高贤又是一个
疾
字脱口
咒语瞬间完成
周烨脸色更难看了
告诫他快不代表对
容易失败
高贤却一脸无辜地问
自己明明感觉到了法印法咒共鸣
难道不对吗
周烨被噎得没话说
只好强调最难的是观想
要体会金枪的霸气神韵
哪知道高贤又小心翼翼地表示
观想好像也
会了
这下周烨有点挂不住脸了
自己练了几年才成
这小子一天就会
他觉得高贤在逗他玩
带着几分薄怒
要求高贤直接对他用一次真阳枪
让他看看真本事
高贤有些为难
怕伤到长辈
周烨却满不在乎
自恃练气九层修为
催促他赶紧动手
高贤无奈
只好凝神施法
观想出金色长枪
但只敢用了三分力道
无声地刺向周烨
起初周烨还一脸轻视
但当高贤眼中闪过淡金光芒
真阳枪凝成的瞬间
他脸色骤变
紧接着
一股如烧红铁针刺入般的剧痛猛地扎进他的眉心
剧痛让他差点失态叫出声
硬是咬牙忍住
老脸痛苦地皱成一团
高贤见状忙问他是
---

请严格按照上述要求输出分组编号，确保每组都能成为一幅有画面感的插画描述。
**不要给出任何分组的解释说明，只输出分组编号结果，每行一个分组。**'''

payload = {
    "model": "deepseek/deepseek-r1:free",
    "messages": [
        {"role": "user", "content": ai_grouping_prompt}
    ],
    "reasoning": {
        "effort": "high",
        "exclude": True
    }
}

response = requests.post(url, headers=headers, data=json.dumps(payload))

try:
    data = response.json()
    print("\n--- Raw Response ---\n")
    print(json.dumps(data, indent=2, ensure_ascii=False))
    print("\n--- Model Output ---\n")
    print(data['choices'][0]['message']['content'])
except Exception as e:
    print("解析响应时出错：", e)
    print(response.text)
