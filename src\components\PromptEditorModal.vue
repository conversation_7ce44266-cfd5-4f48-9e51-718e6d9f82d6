<template>
  <div
    v-if="show"
    class="prompt-editor-modal-overlay"
  >
    <div class="prompt-editor-modal-content">
      <h3 class="modal-title">
        编辑AI指令 (Prompt)
      </h3>
      <div class="prompt-type-row">
        <label for="promptTypeSelect">选择类型：</label>
        <select id="promptTypeSelect" v-model="selectedType">
          <option v-for="(label, key) in promptTypeOptions" :key="key" :value="key">{{ label }}</option>
        </select>
      </div>
      <textarea
        v-model="editablePromptContent"
        class="prompt-textarea"
      />
      <div class="modal-actions">
        <button
          @click="handleSave"
          class="modal-button save-button"
        >
          保存
        </button>
        <button
          @click="handleRestoreDefault"
          class="modal-button restore-button"
        >
          恢复默认
        </button>
        <button
          @click="closeModal"
          class="modal-button close-button"
        >
          关闭
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, watch, toRefs } from 'vue';

export default {
  name: 'PromptEditorModal',
  props: {
    show: Boolean,
    promptsMap: Object, // { type: promptString }
    defaultPromptsMap: Object, // { type: defaultPromptString }
    currentType: String, // 当前选中的类型
    promptTypeOptions: { // { type: label }
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:show', 'save-prompt', 'update:type'],
  setup(props, { emit }) {
    const { promptsMap, defaultPromptsMap, show, currentType } = toRefs(props);
    const selectedType = ref(currentType.value || Object.keys(props.promptTypeOptions)[0]);
    const editablePromptContent = ref('');

    // 切换类型时，加载对应内容（兜底：user 为空时用 default）
    watch([show, selectedType, promptsMap, defaultPromptsMap], ([modalShow, type, map, defMap]) => {
      if (modalShow) {
        editablePromptContent.value = (map && map[type])
          ? map[type]
          : (defMap && defMap[type]) || '';
      }
    }, { immediate: true });

    // 外部切换类型时同步
    watch(currentType, (newType) => {
      if (show.value) selectedType.value = newType;
    });

    // 选择类型时通知父组件
    watch(selectedType, (val) => {
      emit('update:type', val);
    });

    const closeModal = () => {
      emit('update:show', false);
    };

    const handleSave = () => {
      emit('save-prompt', { type: selectedType.value, content: editablePromptContent.value });
      closeModal();
    };

    const handleRestoreDefault = () => {
      editablePromptContent.value = (defaultPromptsMap.value && defaultPromptsMap.value[selectedType.value]) || '';
    };

    return {
      editablePromptContent,
      closeModal,
      handleSave,
      handleRestoreDefault,
      selectedType
    };
  }
}
</script>

<style scoped>
.prompt-editor-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2050; /* Increased z-index to be consistent with TextViewerModal */
}

.prompt-editor-modal-content {
  background-color: #2a273f; /* Dark theme background */
  padding: 25px;
  border-radius: 8px;
  border: 1px solid #44415a;
  width: 60%;
  max-width: 700px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.3);
  display: flex;
  flex-direction: column;
  resize: vertical;
  margin-bottom: 20px;
  font-family: inherit; /* Ensuring consistent font */
  box-sizing: border-box; /* Ensures padding and border don't increase total width/height */
}

.modal-title {
  font-size: 1.3rem;
  color: #e0def4;
  margin-bottom: 15px;
  text-align: center;
}

.prompt-type-row {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
}
.prompt-type-row label {
  color: #b8b5d6;
  font-size: 0.98em;
}
.prompt-type-row select {
  background: #232136;
  color: #e0def4;
  border: 1px solid #3e8fb0;
  border-radius: 4px;
  padding: 4px 12px;
  font-size: 1em;
}

.prompt-textarea {
  width: 100%;
  min-height: 250px;
  background-color: #232136;
  border: 1px solid #44415a;
  border-radius: 4px;
  color: #e0def4;
  padding: 10px;
  font-size: 0.9rem;
  resize: vertical;
  margin-bottom: 20px;
  font-family: inherit;
}

.prompt-textarea:focus {
  border-color: #3e8fb0;
  outline: none;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 15px; /* Add padding to separate from textarea */
  border-top: 1px solid #44415a; /* Separator line */
}

.modal-button {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s, border-color 0.2s;
  border: 1px solid transparent;
}

.save-button {
  background-color: #3e8fb0; /* Primary action */
  color: #ffffff; /* White text */
  border-color: #3e8fb0;
}
.save-button:hover {
  background-color: #4ea8c6;
  border-color: #4ea8c6;
}

.restore-button {
  background-color: #555c6b; /* Darker secondary button, consistent with classification modal */
  color: #e0def4; /* Light text */
  border: 1px solid #44415a; /* Consistent border */
}
.restore-button:hover {
  background-color: #666f7d;
  border-color: #555c6b;
}

.close-button {
  background-color: transparent;
  color: #908caa;
  border: 1px solid #908caa;
}
.close-button:hover {
  background-color: #44415a; /* Slight background on hover for better feedback */
  color: #e0def4;
  border-color: #908caa; /* Keep border color or make it lighter e.g. #b8b5d6 */
}
</style> 
