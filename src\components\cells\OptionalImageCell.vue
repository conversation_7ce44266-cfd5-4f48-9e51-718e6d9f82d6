<template>
  <div class="grid-cell optional-image-cell">
    <div class="optional-wrapper">
      <div class="thumbnails-container">
        <div class="thumbnails-grid">
          <!-- 显示四个默认的空白缩略图 -->
          <div
            class="image-item"
            v-for="index in 4"
            :key="index"
            @click="selectThumbnail(index-1)"
          >
            <img
              v-if="thumbnails[index-1]"
              :src="thumbnails[index-1]"
              :alt="`缩略图${index}`"
              class="thumbnail-image"
            >
            <div class="sequence-badge">
              {{ index }}
            </div>
          </div>
        </div>
      </div>
      <div
        class="vertical-manage-btn"
        @click="manageImages"
      >
        <div class="vertical-text">
          管理图片
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OptionalImageCell',
  emits: ['select-thumbnail', 'manage-images'],
  props: {
    thumbnails: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    selectThumbnail(index) {
      this.$emit('select-thumbnail', index);
    },
    manageImages() {
      this.$emit('manage-images');
    }
  }
}
</script>

<style scoped>
.grid-cell {
  display: table-cell;
  text-align: center;
  vertical-align: top;
  border-right: 1px solid #333;
  border-bottom: 1px solid #333;
  color: #e0e0e0;
  box-sizing: border-box;
  font-size: 0.9rem;
  padding: 0;
  overflow: hidden;
}

.optional-image-cell {
  width: 15%;
}

.optional-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  background-color: #252525;
  gap: 4px;
}

.thumbnails-container {
  flex: 1;
  overflow-y: auto;
  background-color: #1e1e1e;
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #444 #222;
}

.thumbnails-container::-webkit-scrollbar {
  width: 6px;
}

.thumbnails-container::-webkit-scrollbar-track {
  background: #1a1a1a;
}

.thumbnails-container::-webkit-scrollbar-thumb {
  background-color: #444;
  border-radius: 3px;
}

.thumbnails-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-auto-rows: minmax(80px, 1fr);
  gap: 4px;
  padding: 4px;
  min-height: 200px;
}

.image-item {
  background-color: #111;
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 80px;
  cursor: pointer;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed #333;
}

.thumbnail-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  background-color: #111;
}

.sequence-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  background-color: #ff3d7f;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  z-index: 1;
}

.vertical-manage-btn {
  width: 30px;
  background-color: #1e1e1e;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.vertical-manage-btn:hover {
  background-color: #2d2d2d;
}

.vertical-text {
  writing-mode: vertical-rl;
  text-orientation: upright;
  color: #ccc;
  font-size: 12px;
  letter-spacing: 2px;
  user-select: none;
}
</style> 