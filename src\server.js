const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const localCommunication = require('./server/localCommunication');

const app = express();

// 启用跨域
app.use(cors());

// 解析 JSON 请求体
app.use(bodyParser.json());

// 添加简单的健康检查接口
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 挂载本地通信路由
app.use('/api/local', localCommunication);

// 全局错误处理
app.use((err, req, res) => {
  console.error('全局错误:', err);
  res.status(500).json({ error: err.message || '服务器内部错误' });
});

// 使用8091端口，与前端配置匹配
const PORT = process.env.PORT || 8091;

// 启动服务器
app.listen(PORT, () => {
  console.log(`===========================================`);
  console.log(`Server running at http://localhost:${PORT}`);
  console.log(`Backend API available at /api/local/*`);
  console.log(`===========================================`);
});

// 处理未捕获的异常
process.on('uncaughtException', (err) => {
  console.error('未捕获的异常:', err);
});

// 处理未处理的 Promise 拒绝
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的 Promise 拒绝:', reason);
}); 