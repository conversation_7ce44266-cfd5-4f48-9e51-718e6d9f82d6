<template>
  <div class="studio-container">
    <!-- 使用步骤导航组件，放在顶部 -->
    <StepsNavigationBar
      :project-title="projectData?.title || '未命名项目'"
      :chapter-title="localChapterTitle"
      :current-step="currentStep"
      :has-srt-file="hasSrtFile"
      :has-audio-file="hasAudioFile"
      :use-builtin-audio="useBuiltinAudio"
      :show-export-button="true"
      @go-back="goBack"
      @next-step="goToNextStep"
      @update-title="updateProjectTitle"
      @navigate-to-step="navigateToStep"
      @show-error="showErrorMessage"
      @export-import="handleExportImport"
    />

    <!-- 次级导航栏 -->
    <SecondaryNavbar
      :drawing-count="0"
      :total-drawings="129"
      :active-tab="activeNavTab"
      :steps="['match-tags', 'settings']"
      @tab-change="handleTabChange"
      @action="handleNavAction"
    />

    <!-- 添加内容刷新按钮 -->
    <div class="action-buttons">
      <button
        class="refresh-btn"
        @click="forceRefreshSrtContent"
        title="强制刷新SRT内容"
      >
        <i class="ri-refresh-line" /> 强制刷新SRT内容
      </button>
    </div>

    <!-- 选择状态栏 -->
    <div class="selection-status-bar">
      <div class="selection-info">
        已选择 {{ selectedRowCount }} 项
      </div>
    </div>

    <!-- SRT数据加载中或无数据时显示 -->
    <div
      v-if="isLoading"
      class="no-data-message"
    >
      <div class="message-icon">
        <i class="icon-loading" />
      </div>
      <div class="message-text">
        {{ loadingMessage }}
      </div>
    </div>

    <!-- 无数据但已加载完成时显示 -->
    <div
      v-if="!isLoading && !hasRows"
      class="no-data-message"
    >
      <div class="message-icon">
        📄
      </div>
      <div class="message-text">
        没有找到SRT字幕数据
      </div>
      <div class="message-subtext">
        请上传SRT文件或选择已有的SRT文件
      </div>
      <RouterLink
        to="/creation"
        class="back-button"
      >
        返回创建页面
      </RouterLink>
    </div>

    <!-- 使用模块化的网格布局 -->
    <GridLayout v-if="hasRows">
      <GridHeader />

      <!-- 动态生成多行数据 -->
      <GridRow
        v-for="(row, index) in rows"
        :key="index"
      >
        <SelectCell
          :is-selected="row.isSelected"
          :is-first-row="index === 0"
          :is-merged="row.isMerged"
          @toggle-select="toggleRowSelection(index)"
          @merge-up="handleMergeUp(index)"
          @split-down="handleSplitDown(index)"
        />

        <IndexCell :row-index="index" />
        <DescriptionCell :description="row.description" />

        <TagsCell :tags="[]" />

        <KeywordCell
          :keywords="row.keywords"
          @update:keywords="value => updateKeywords(index, value)"
        />

        <ImageCell
          :image-src="row.imageSrc"
          :image-alt="row.imageAlt"
          :is-locked="row.isImageLocked"
          @select-image="() => handleSelectMainImage(index)"
          @toggle-lock="() => handleToggleImageLock(index)"
          @clear-image="() => handleClearMainImage(index)"
        />

        <OptionalImageCell
          :thumbnails="row.thumbnails"
          @select-thumbnail="thumbnailIndex => handleSelectThumbnail(index, thumbnailIndex)"
          @manage-images="() => handleManageImages(index)"
        />

        <OperationCell
          @redraw-image="() => handleRedrawImage(index)"
          @infer-prompt="() => handleInferPrompt(index)"
        />
      </GridRow>
    </GridLayout>

    <!-- 添加Toast通知组件 -->
    <ToastNotification
      ref="toast"
      :message="toastMessage"
      :title="toastTitle"
      :type="toastType"
      :duration="5000"
    />
  </div>
</template>

<script>
import StepsNavigationBar from './StepsNavigationBar.vue';
import SecondaryNavbar from './SecondaryNavbar.vue';
import ToastNotification from './ToastNotification.vue';

// 导入网格布局组件
import GridLayout from './grid/GridLayout.vue';
import GridHeader from './grid/GridHeader.vue';
import GridRow from './grid/GridRow.vue';

// 导入单元格组件
import SelectCell from './cells/SelectCell.vue';
import IndexCell from './cells/IndexCell.vue';
import DescriptionCell from './cells/DescriptionCell.vue';
import TagsCell from './cells/TagsCell.vue';
import KeywordCell from './cells/KeywordCell.vue';
import ImageCell from './cells/ImageCell.vue';
import OptionalImageCell from './cells/OptionalImageCell.vue';
import OperationCell from './cells/OperationCell.vue';

// 导入业务逻辑
import { useStudioActions } from '../composables/useStudioActions';
import { provideProjectContext } from '../composables/useProjectContext.js';


export default {
  name: 'ContentCreationStudio',
  components: {
    StepsNavigationBar,
    SecondaryNavbar,
    ToastNotification,
    GridLayout,
    GridHeader,
    GridRow,
    SelectCell,
    IndexCell,
    DescriptionCell,
    TagsCell,
    KeywordCell,
    ImageCell,
    OptionalImageCell,
    OperationCell,
  },
  emits: ['navigate', 'open-adjust-shots-drawer', 'open-global-reasoning-drawer'],
  props: {
    project: {
      type: Object,
      required: false,
      default: () => null
    },
    projectTitle: {
      type: String,
      required: false,
      default: null
    },
    chapterTitle: {
      type: String,
      required: false,
      default: null
    }
  },
  setup() {
    // 提供项目上下文
    const projectContextTools = provideProjectContext();

    // 使用抽取的业务逻辑
    const studioActions = useStudioActions();

    // 返回项目上下文工具，以便在选项式API中使用
    return {
      ...studioActions,
      projectContextTools
    };
  },
  data() {
    return {
      // 项目数据
      localProjectTitle: '',
      projectData: {},
      localChapterTitle: '',  // 添加章节标题
      // 步骤导航相关数据
      currentStep: 'edit',  // 默认为编辑模式
      // SecondaryNavbar相关数据
      activeNavTab: 'match-tags',
      // StepsNavigationBar所需数据
      hasSrtFile: false,
      hasAudioFile: false,
      useBuiltinAudio: false,

      // 行数据数组
      rows: [],

      // 加载状态
      isLoading: true,
      loadingMessage: '正在加载SRT数据，请稍候...',

      // 服务器基础URL
      serverBaseUrl: 'http://localhost:8080',

      // 默认空行模板
      emptyRow: {
        isSelected: false,
        description: '',
        keywords: '',
        imageSrc: 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7',
        imageAlt: '',
        isImageLocked: false,
        thumbnails: []
      },

      // Toast通知相关数据
      toastType: '',
      toastTitle: '',
      toastMessage: ''
    }
  },
  computed: {
    // 判断是否有行数据
    hasRows() {
      return this.rows.length > 0;
    },

    // 计算选中的行数
    selectedRowCount() {
      return this.rows.filter(row => row.isSelected).length;
    }
  },
  async created() {
    console.log('ContentCreationStudio组件被创建');

    // 初始化项目信息
    await this.initializeProjectInfo();

    // 加载项目数据
    await this.loadSrtContent();

    // 加载完成后自动检测SRT文件更新
    this.$nextTick(async () => {
      // 延迟一下，确保UI已渲染
      setTimeout(async () => {
        // 自动检测SRT变更
        const hasChanges = await this.autoDetectSrtChanges();
        if (hasChanges) {
          console.log('已自动检测到SRT文件变更并已刷新');
        }
      }, 500);
    });
  },
  mounted() {
    // 初始化项目上下文
    if (this.projectData && this.projectContextTools) {
      console.log('[ContentCreationStudio] mounted - 初始化项目上下文:', this.projectData);
      this.projectContextTools.setProjectContext({
        projectTitle: this.projectData.title || '',
        chapterTitle: this.projectData.currentChapter || '',
        currentStep: this.currentStep || '',
        projectData: this.projectData
      });
    }
  },
  beforeUnmount() {
    // 无需清除定时器
  },
  methods: {
    // 添加初始化项目信息的方法
    async initializeProjectInfo() {
      // 尝试获取服务器基础URL
      await this.getServerBaseUrl();

      // 获取项目和章节信息，优先使用props
      let projectFromProps = this.projectTitle || '';
      let chapterFromProps = this.chapterTitle || '';

      // 如果props中没有，尝试从URL获取
      if (!projectFromProps || !chapterFromProps) {
        const urlParams = new URLSearchParams(window.location.search);
        projectFromProps = projectFromProps || urlParams.get('project');
        chapterFromProps = chapterFromProps || urlParams.get('chapter');
      }

      console.log('项目和章节信息:', projectFromProps, chapterFromProps);

      // 检查是否有合并操作的标志
      const hasMerged = projectFromProps && chapterFromProps ?
        localStorage.getItem(`project_${projectFromProps}_${chapterFromProps}_hasMerged`) : null;
      console.log('检查是否有合并操作的标志:', hasMerged);

      // 初始化项目数据
      if (this.project) {
        // 如果通过props传递了项目数据，优先使用它
        this.localProjectTitle = this.project.title || '未命名项目';
        this.projectData = this.project;
        console.log('从props初始化项目数据:', this.projectData);
      } else if (projectFromProps && chapterFromProps) {
        // 如果有项目和章节信息，尝试从文件系统加载
        console.log('从props或URL加载项目数据:', projectFromProps, chapterFromProps);
        this.localProjectTitle = projectFromProps;
        this.localChapterTitle = chapterFromProps;

        // 如果有合并操作的标志，直接从project-data.json文件中加载数据
        if (hasMerged === 'true') {
          console.log('检测到合并操作标志，直接从project-data.json文件中加载数据');
          const success = await this.loadProjectDataFromFile(projectFromProps, chapterFromProps);
          if (success) {
            console.log('成功从project-data.json文件中加载数据，包含合并状态');
            return;
          } else {
            console.warn('无法从project-data.json文件中加载数据，尝试常规加载');
          }
        }
      } else {
        console.warn('没有找到项目数据，无法初始化');
        this.isLoading = false;
        return;
      }

      // 设置文件状态
      if (this.projectData && this.projectData.data) {
        this.hasSrtFile = !!this.projectData.data.srtFilePath;
        this.hasAudioFile = !!this.projectData.data.audioFilePath;
        this.useBuiltinAudio = !!this.projectData.data.useBuiltinAudio;

        // 获取章节标题
        if (this.projectData.data.currentChapter) {
          this.localChapterTitle = this.projectData.data.currentChapter;
          console.log('从项目数据中获取章节标题:', this.localChapterTitle);
        } else if (this.projectData.currentChapter) {
          this.localChapterTitle = this.projectData.currentChapter;
          console.log('从项目对象中获取章节标题:', this.localChapterTitle);
        } else {
          // 尝试从文件路径中提取章节名称
          const srtPath = this.projectData.data.srtFilePath;
          if (srtPath) {
            const pathParts = srtPath.split('/');
            if (pathParts.length >= 3) {
              // 假设路径格式为 draft/项目名/章节名/文件名
              this.localChapterTitle = pathParts[2];
              console.log('从文件路径中提取章节标题:', this.localChapterTitle);
            }
          }
        }

        // 如果项目已经有行数据，加载它们
        if (this.projectData.data.rows && this.projectData.data.rows.length > 0) {
          console.log('使用项目中已有的行数据, 行数:', this.projectData.data.rows.length);

          // 确保所有行数据都有正确的属性，特别是合并和分拆相关的属性
          this.rows = this.projectData.data.rows.map((row, rowIndex) => {
            // 确保每行都有 originalIndex 属性 - 这是新的关键属性
            if (row.originalIndex === undefined) {
              row.originalIndex = row.index || rowIndex + 1;
            }

            // 确保每行都有 originalState 属性
            if (!row.originalState) {
              row.originalState = {
                originalIndex: row.originalIndex,
                index: row.index || rowIndex + 1,
                startTime: row.startTime,
                endTime: row.endTime,
                duration: row.duration,
                description: row.description
              };
            } else if (row.originalState.originalIndex === undefined) {
              // 添加 originalIndex 到现有的 originalState
              row.originalState.originalIndex = row.originalIndex;
            }

            // 确保 isMerged 属性存在
            if (row.isMerged === undefined) {
              row.isMerged = false;
            }

            // 确保 mergedRows 属性存在（如果是合并行）
            if (row.isMerged && !row.mergedRows) {
              row.mergedRows = [];
            }

            // 如果是合并行，确保所有合并项都有 originalIndex
            if (row.isMerged && row.mergedRows && row.mergedRows.length > 0) {
              row.mergedRows = row.mergedRows.map((mergedRow, mrIndex) => {
                if (mergedRow.originalIndex === undefined) {
                  // 生成一个唯一的 originalIndex
                  mergedRow.originalIndex = this.projectData.data.rows.length + mrIndex + 1;
                }
                return mergedRow;
              });
            }

            return row;
          });

          // 添加调试日志
          const mergedRows = this.rows.filter(row => row.isMerged);
          console.log('行数据加载完成，包含合并行数量:', mergedRows.length);
          if (mergedRows.length > 0) {
            console.log('合并行详情:', mergedRows.map(row => ({
              index: row.index,
              originalIndex: row.originalIndex,
              isMerged: row.isMerged,
              mergedRowsCount: row.mergedRows ? row.mergedRows.length : 0
            })));

            // 设置合并标志
            localStorage.setItem(`project_${this.localProjectTitle}_${this.localChapterTitle}_hasMerged`, 'true');
          }

          this.isLoading = false;
        }
      } else {
        console.warn('项目数据不完整');
        this.isLoading = false;
      }
    },
    // 从项目数据中加载SRT内容 - 简化版
    async loadSrtContent() {
      try {
        console.log('开始加载SRT内容，项目数据:', JSON.stringify(this.projectData, null, 2));

        // 检查是否有合并操作的标志
        const hasMerged = localStorage.getItem(`project_${this.localProjectTitle}_${this.localChapterTitle}_hasMerged`);
        console.log('合并操作标志:', hasMerged, '项目标题:', this.localProjectTitle, '章节标题:', this.localChapterTitle);

        // 先尝试从project-data.json文件加载数据（如果有合并状态）
        if (hasMerged === 'true') {
          console.log('检测到合并操作标志，尝试从project-data.json文件加载数据');
          const success = await this.loadProjectDataFromFile(this.localProjectTitle, this.localChapterTitle);
          if (success) {
            console.log('成功从project-data.json加载数据（含合并状态）');
            return;
          } else {
            console.warn('无法从project-data.json加载数据，继续尝试其他方法');
          }
        }

        // 检查项目数据中是否已经有行数据（包含合并状态）
        if (this.projectData.data?.rows?.length > 0) {
          console.log('项目数据中已有行数据，数量:', this.projectData.data.rows.length);
          console.log('行数据示例:', JSON.stringify(this.projectData.data.rows[0], null, 2));
          const existingMergedRows = this.projectData.data.rows.filter(row => row.isMerged);
          console.log('已存在的合并行数量:', existingMergedRows.length);

          if (existingMergedRows.length > 0) {
            // 直接使用现有行数据
            console.log('使用已有的行数据（保留合并状态）');
            this.rows = this.projectData.data.rows;
            this.isLoading = false;
            this.showSuccessMessage('数据加载成功', `成功加载 ${this.rows.length} 条字幕（保留合并状态）`);
            localStorage.setItem(`project_${this.localProjectTitle}_${this.localChapterTitle}_hasMerged`, 'true');
            return;
          }
        }

        // 如果项目有SRT内容，直接解析
        if (this.projectData.data?.srtContent) {
          console.log('项目中有SRT内容，直接解析:', this.projectData.data.srtContent.substring(0, 100) + '...');
          const parsedSubtitles = this.parseSrtContent(this.projectData.data.srtContent);
          console.log('解析完成，字幕数量:', parsedSubtitles.length);
          console.log('第一条字幕内容:', JSON.stringify(parsedSubtitles[0], null, 2));
          this.convertSubtitlesToRows(parsedSubtitles);
          return;
        }

        // 如果有SRT文件路径，尝试读取SRT文件内容
        if (this.projectData.data?.srtFilePath) {
          try {
            console.log('项目中有SRT文件路径，尝试读取文件:', this.projectData.data.srtFilePath);

            // 首先检查文件是否存在
            const checkResponse = await fetch(`/api/local/check-file-exists?filePath=${encodeURIComponent(this.projectData.data.srtFilePath)}`);
            const checkResult = await checkResponse.json();

            if (!checkResult.exists) {
              console.error('SRT文件不存在:', this.projectData.data.srtFilePath);
              throw new Error('SRT文件不存在');
            }

            console.log('SRT文件存在，开始读取内容');

            // 读取SRT文件内容
            const response = await fetch(`/api/local/read-file?path=${encodeURIComponent(this.projectData.data.srtFilePath)}`);

            if (!response.ok) {
              console.error('SRT文件读取失败，状态码:', response.status);
              throw new Error(`无法读取SRT文件: ${response.status}`);
            }

            const result = await response.json();

            if (!result.success || !result.content) {
              console.error('SRT文件读取结果无效:', result);
              throw new Error('SRT文件内容无效');
            }

            console.log('SRT文件读取成功，内容长度:', result.content.length);
            const srtContent = result.content;

            // 保存SRT内容到项目数据
            this.projectData.data.srtContent = srtContent;

            // 解析SRT内容并转换为行数据
            console.log('开始解析SRT内容...');
            const parsedSubtitles = this.parseSrtContent(srtContent);
            console.log('SRT解析完成，字幕数量:', parsedSubtitles.length);

            if (parsedSubtitles.length === 0) {
              console.error('解析SRT内容后没有字幕');
              throw new Error('SRT解析结果为空');
            }

            this.convertSubtitlesToRows(parsedSubtitles);
            console.log('字幕转换为行数据成功，行数:', this.rows.length);

            // 保存项目数据
            this.saveProject(this.projectData);
            return;
          } catch (error) {
            console.error('读取SRT文件出错:', error);
            this.showErrorMessage(`读取SRT文件失败: ${error.message}`);
          }
        } else {
          console.warn('项目数据中没有SRT文件路径');
        }

        // 如果以上方法都失败，使用测试数据
        console.warn('无法获取SRT内容，使用测试数据');
        this.showErrorMessage('无法加载SRT文件内容，使用测试数据代替');
        this.useTestData();
      } catch (error) {
        console.error('加载SRT内容失败:', error);
        this.showErrorMessage('加载SRT内容失败: ' + error.message);
        this.useTestData();
      } finally {
        this.isLoading = false;
      }
    },

    // 简化版读取文件内容的方法
    async readFileContent(filePath) {
      try {
        console.log('读取文件内容:', filePath);
        const response = await fetch(`/api/local/read-file?path=${encodeURIComponent(filePath)}`);

        if (response.ok) {
          const result = await response.json();
          if (result.success && result.content) {
            return result.content;
          }
        }

        throw new Error(`无法读取文件: ${response.status}`);
      } catch (error) {
        console.error('读取文件内容失败:', error);
        throw error;
      }
    },

    // 使用测试数据 - 保持不变
    useTestData() {
      console.log('使用测试数据');
      const testSubtitles = [
        { index: 1, startTime: 1000, endTime: 5000, duration: 4000, text: '这是测试字幕1' },
        { index: 2, startTime: 6000, endTime: 10000, duration: 4000, text: '这是测试字幕2' },
        { index: 3, startTime: 11000, endTime: 15000, duration: 4000, text: '这是测试字幕3\n包含多行文本' },
        { index: 4, startTime: 16000, endTime: 20000, duration: 4000, text: '这是测试字幕4' },
        { index: 5, startTime: 21000, endTime: 25000, duration: 4000, text: '这是测试字幕5' }
      ];

      this.convertSubtitlesToRows(testSubtitles);
      this.isLoading = false;
    },

    // 获取服务器基础URL - 简化版
    async getServerBaseUrl() {
      this.serverBaseUrl = 'http://localhost:8080';
      console.log('使用固定服务器URL:', this.serverBaseUrl);
      return this.serverBaseUrl;
    },

    // 构建API URL
    getApiUrl(path) {
      return `${this.serverBaseUrl}/api/${path}`;
    },

    // 从文件系统加载项目数据 - 简化版
    async loadProjectDataFromFile(projectTitle, chapterTitle) {
      try {
        console.log('从文件系统加载项目数据:', projectTitle, chapterTitle);

        // 构建项目数据文件路径
        const projectDataPath = `draft/${projectTitle}/${chapterTitle}/project-data.json`;

        // 获取当前目录中的SRT文件
        const filesPath = `draft/${projectTitle}/${chapterTitle}`;
        const filesResponse = await fetch(`/api/local/list-files?path=${encodeURIComponent(filesPath)}`);
        const currentSrtFile = filesResponse.ok ?
          (await filesResponse.json()).find(file => file.toLowerCase().endsWith('.srt')) : null;

        // 尝试读取项目数据文件
        const response = await fetch(`/api/local/read-file?path=${encodeURIComponent(projectDataPath)}`);

        if (!response.ok) {
          console.error('无法读取项目数据文件:', response.status);
          return false;
        }

        const result = await response.json();

        if (!result.success || !result.content) {
          console.error('项目数据文件内容无效');
          return false;
        }

        // 解析项目数据
        const projectData = JSON.parse(result.content);

        // 检查SRT文件变更
        let forceReloadSrt = false;
        if (projectData.srtFilePath) {
          const savedSrtFile = projectData.srtFilePath.split('/').pop();
          if (currentSrtFile && savedSrtFile && currentSrtFile !== savedSrtFile) {
            forceReloadSrt = true;
          }
        }

        // 创建完整的项目对象
        this.projectData = {
          title: projectTitle,
          currentChapter: chapterTitle,
          data: projectData
        };

        // 更新URL
        const url = new URL(window.location.href);
        url.searchParams.set('project', projectTitle);
        url.searchParams.set('chapter', chapterTitle);
        window.history.replaceState({}, '', url.toString());

        // 如果SRT文件变更，加载新内容
        if (forceReloadSrt && currentSrtFile) {
          const srtPath = `${filesPath}/${currentSrtFile}`;
          const srtContent = await this.readFileContent(srtPath);

          // 更新项目数据
          this.projectData.data.srtFilePath = srtPath;
          this.projectData.data.srtFile = currentSrtFile;
          this.projectData.data.srtContent = srtContent;

          // 解析新内容
          const parsedSubtitles = this.parseSrtContent(srtContent);
          this.convertSubtitlesToRows(parsedSubtitles);

          // 保存更新后的项目数据
          this.saveProject(this.projectData);
        } else {
          // 使用现有数据
          if (projectData.rows?.length > 0) {
            // 确保所有行数据属性正确
            this.rows = projectData.rows.map(row => {
              // 创建更新后的行对象
              const updatedRow = {
                ...row,
                originalState: row.originalState || {
                  index: row.index,
                  startTime: row.startTime,
                  endTime: row.endTime,
                  duration: row.duration,
                  description: row.description,
                  mergedWith: []
                },
                isMerged: row.isMerged !== undefined ? row.isMerged : false,
                mergedRows: (row.isMerged && !row.mergedRows) ? [] : row.mergedRows
              };

              // 确保每行都有tags属性
              if (!updatedRow.tags || !Array.isArray(updatedRow.tags)) {
                updatedRow.tags = []; // 使用空数组
              }

              return updatedRow;
            });

            this.isLoading = false;

            // 记录合并状态
            if (this.rows.some(row => row.isMerged)) {
              localStorage.setItem(`project_${projectTitle}_${chapterTitle}_hasMerged`, 'true');
            }
          }
        }

        return true;
      } catch (error) {
        console.error('加载项目数据出错:', error);
        this.showErrorMessage('加载项目数据出错: ' + error.message);
        return false;
      }
    },

    // 保存项目数据 - 保持不变
    async saveProject(projectData) {
      if (!projectData) return;

      try {
        console.log('保存项目数据...');

        // 创建项目数据的深拷贝以避免修改原始数据
        const projectDataToSave = structuredClone(projectData);

        // 确保行数据中的所有合并和分拆状态信息被正确保存
        if (projectDataToSave.data && projectDataToSave.data.rows) {
          try {
            // 尝试将行数据转换为 JSON 然后再转回对象，检测是否有循环引用
            JSON.stringify(projectDataToSave.data.rows);
          } catch (jsonError) {
            console.error('行数据存在循环引用，尝试修复:', jsonError);

            // 如果出现循环引用，创建一个新的行数组，去除可能导致循环引用的深层嵌套
            const fixedRows = projectDataToSave.data.rows.map(row => {
              const newRow = { ...row };

              // 处理合并行
              if (newRow.isMerged && newRow.mergedRows) {
                // 创建一个简化版的 mergedRows，确保每个合并项都是独立的对象而不是引用
                newRow.mergedRows = newRow.mergedRows.map(mergedRow => ({
                  originalIndex: mergedRow.originalIndex,
                  index: mergedRow.index,
                  description: mergedRow.description,
                  startTime: mergedRow.startTime,
                  endTime: mergedRow.endTime,
                  duration: mergedRow.duration,
                  tags: [], // 空标签数组
                  keywords: mergedRow.keywords || ''
                }));
              }

              return newRow;
            });

            // 用修复后的行替换原始行
            projectDataToSave.data.rows = fixedRows;
          }
        }

        // 构建保存请求
        const saveData = {
          projectTitle: projectDataToSave.title || this.localProjectTitle,
          chapterTitle: this.localChapterTitle,
          data: projectDataToSave.data || {}
        };

        // 发送到服务器保存
        const response = await fetch('/api/local/save-project-data', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(saveData)
        });

        if (response.ok) {
          console.log('项目数据保存成功');

          // 更新URL，添加项目和章节参数
          const url = new URL(window.location.href);
          url.searchParams.set('project', this.localProjectTitle);
          url.searchParams.set('chapter', this.localChapterTitle);
          window.history.replaceState({}, '', url.toString());
        } else {
          console.error('保存项目数据失败:', response.status);
          this.showErrorMessage('保存项目数据失败: ' + response.statusText);
        }
      } catch (error) {
        console.error('保存项目数据出错:', error);
        this.showErrorMessage('保存项目数据出错: ' + error.message);
      }
    },

    // 显示成功消息 - 保持不变
    showSuccessMessage(title, message) {
      this.toastType = 'success';
      this.toastTitle = title;
      this.toastMessage = message;
      this.$nextTick(() => {
        this.$refs.toast.$refs.toastElement.classList.add('show');
      });
    },

    // 显示错误消息 - 保持不变
    showErrorMessage(message, title = '错误') {
      this.toastType = 'error';
      this.toastTitle = title;
      this.toastMessage = message;
      this.$nextTick(() => {
        this.$refs.toast.$refs.toastElement.classList.add('show');
      });
    },

    // 显示信息消息 - 保持不变
    showInfoMessage(title, message) {
      this.toastType = 'info';
      this.toastTitle = title;
      this.toastMessage = message;
      this.$nextTick(() => {
        this.$refs.toast.$refs.toastElement.classList.add('show');
      });
    },

    // 解析SRT内容 - 保持不变
    parseSrtContent(srtContent) {
      try {
        console.log('开始解析SRT内容...');

        if (!srtContent || typeof srtContent !== 'string') {
          console.error('SRT内容无效:', srtContent);
          return [];
        }

        // 去除BOM标记如果存在
        srtContent = srtContent.replace(/^\uFEFF/, '');

        // 检查内容是否为空
        if (srtContent.trim() === '') {
          console.error('SRT内容为空字符串');
          return [];
        }

        console.log('SRT内容前50个字符:', srtContent.substring(0, 50).replace(/\n/g, '\\n'));

        // 按块分割SRT文件（每个字幕是一个块）
        const blocks = srtContent.trim().split(/\r?\n\r?\n/);
        console.log('SRT文件中的字幕块数量:', blocks.length);

        if (blocks.length === 0) {
          console.error('无法找到任何字幕块');
          return [];
        }

        // 打印第一个块作为调试
        if (blocks.length > 0) {
          console.log('第一个字幕块内容:', blocks[0]);
        }

        const rows = [];

        // 解析每个块
        blocks.forEach((block, blockIndex) => {
          try {
            // 跳过空块
            if (!block.trim()) {
              console.warn(`字幕块 #${blockIndex + 1} 为空，跳过`);
              return;
            }

            const lines = block.trim().split(/\r?\n/);
            console.log(`字幕块 #${blockIndex + 1} 共有 ${lines.length} 行`);

            // 需要至少2行（索引、时间范围和文本）
            if (lines.length < 2) {
              console.warn(`字幕块 #${blockIndex + 1} 行数不足，跳过`, lines);
              return;
            }

            // 第一行通常是索引号
            let index = blockIndex + 1; // 默认索引
            let lineIndex = 0;

            // 尝试解析第一行作为索引
            const indexMatch = lines[0].trim().match(/^(\d+)$/);
            if (indexMatch) {
              index = parseInt(indexMatch[1]);
              lineIndex++;
            } else {
              console.warn(`字幕块 #${blockIndex + 1} 第一行不是数字索引:`, lines[0]);
            }

            // 查找并解析时间行
            let timeLineFound = false;
            let startTime = 0;
            let endTime = 0;

            for (; lineIndex < lines.length; lineIndex++) {
              // eslint-disable-next-line no-useless-escape
              const timeRangeMatch = lines[lineIndex].trim().match(/(\d+:\d+:\d+[,.]\d+)\s*-->\s*(\d+:\d+:\d+[,.]\d+)/);
              if (timeRangeMatch) {
                // 解析开始和结束时间
                const startTimeStr = timeRangeMatch[1].replace(',', '.');
                const endTimeStr = timeRangeMatch[2].replace(',', '.');

                // 转换时间为秒数
                startTime = this.timeToSeconds(startTimeStr);
                endTime = this.timeToSeconds(endTimeStr);
                timeLineFound = true;
                lineIndex++; // 移动到下一行（文本内容开始）
                break;
              }
            }

            if (!timeLineFound) {
              console.warn(`字幕块 #${blockIndex + 1} 未找到时间行，跳过`);
              return;
            }

            const duration = endTime - startTime;

            // 获取字幕文本（剩余的所有行）
            const text = lines.slice(lineIndex).join('\n');

            if (!text.trim()) {
              console.warn(`字幕块 #${blockIndex + 1} 没有文本内容，跳过`);
              return;
            }

            // 创建行对象
            const row = {
              index,  // 原始SRT索引
              rowId: `row-${Date.now()}-${index}`,  // 唯一行ID
              startTime,  // 开始时间（秒）
              endTime,    // 结束时间（秒）
              duration,   // 持续时间（秒）
              text,       // 字幕文本
              description: text,  // 描述（与文本相同）
              startTimeFormatted: this.formatTime(startTime),  // 格式化的开始时间
              endTimeFormatted: this.formatTime(endTime),      // 格式化的结束时间
              durationFormatted: this.formatDuration(duration), // 格式化的持续时间
              isMerged: false,    // 是否是合并行
              mergedRows: [],     // 合并的行（如果是合并行）
              originalState: {    // 原始状态（用于恢复）
                index,
                startTime,
                endTime,
                duration,
                description: text,
                mergedWith: []
              }
            };

            rows.push(row);
          } catch (error) {
            console.error(`解析字幕块 #${blockIndex + 1} 时出错:`, error);
          }
        });

        console.log('成功解析SRT内容，得到行数据:', rows.length);
        return rows;
      } catch (error) {
        console.error('解析SRT内容时出错:', error);
        return [];
      }
    },

    // 时间字符串转换为秒数 - 保持不变
    timeToSeconds(timeStr) {
      try {
        const [h, m, s] = timeStr.split(':').map(Number);
        return h * 3600 + m * 60 + s;
      } catch (error) {
        console.error('时间转换错误:', error, timeStr);
        return 0;
      }
    },

    // 秒数转换为格式化时间 - 保持不变
    formatTime(seconds) {
      const h = Math.floor(seconds / 3600);
      const m = Math.floor((seconds % 3600) / 60);
      const s = Math.floor(seconds % 60);
      return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
    },

    // 格式化持续时间 - 保持不变
    formatDuration(seconds) {
      const minutes = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${minutes}:${secs.toString().padStart(2, '0')}`;
    },

    // 转换字幕数据为行数据
    convertSubtitlesToRows(subtitles, forceRefresh = false) {
      if (!subtitles || subtitles.length === 0) {
        console.warn('没有字幕数据可转换');
        this.isLoading = false;
        this.loadingMessage = '未找到字幕数据';
        return;
      }

      this.isLoading = true;
      console.log('开始转换字幕为行数据，字幕数量:', subtitles.length);
      console.log('第一条字幕内容:', JSON.stringify(subtitles[0], null, 2));

      // 在上传了新的SRT文件时，强制创建全新的行数据，不考虑保留合并状态
      this.rows = subtitles.map((subtitle, index) => {
        // 创建基本行对象
        const row = {
          ...JSON.parse(JSON.stringify(this.emptyRow)),
          description: subtitle.text,
          startTime: subtitle.startTime,
          endTime: subtitle.endTime,
          duration: subtitle.duration,
          index: index + 1,  // 显示序号，从1开始
          originalIndex: subtitle.index || (index + 1),  // 原始序号，使用SRT中的序号或当前索引
          originalState: {
            originalIndex: subtitle.index || (index + 1),
            index: index + 1,
            startTime: subtitle.startTime,
            endTime: subtitle.endTime,
            duration: subtitle.duration,
            description: subtitle.text
          },
          isMerged: false,
          mergedRows: [],
          tags: [] // 空标签数组，将来实现功能
        };

        return row;
      });

      console.log('转换完成，行数据数量:', this.rows.length);
      console.log('第一行数据示例:', JSON.stringify(this.rows[0], null, 2));

      // 更新项目数据并保存
      if (this.projectData.data) {
        this.projectData.data.rows = this.rows;

        if (!this.projectData.data.originalSrtContent && this.projectData.data.srtContent) {
          this.projectData.data.originalSrtContent = this.projectData.data.srtContent;
        }

        this.saveProject(this.projectData);
      }

      this.isLoading = false;

      const message = forceRefresh ?
        `强制刷新成功，已加载 ${subtitles.length} 条字幕` :
        `成功加载 ${subtitles.length} 条字幕`;
      this.showSuccessMessage('数据加载成功', message);
    },

    // 导航方法保持不变
    goBack() {
      // 直接返回到主页
      console.log('ContentCreationStudio: 返回到主页(Homepage)');

      // 导航到home页面
      this.$emit('navigate', 'home');
    },

    goToNextStep() {
      if (this.currentStep === 'edit') {
        this.currentStep = 'preview';
      }
    },

    updateProjectTitle(newTitle) {
      // 检查是否是章节标题更新
      if (this.localChapterTitle) {
        console.log('更新章节标题:', newTitle);
        this.localChapterTitle = newTitle;
        // 更新项目数据中的章节标题
        if (this.projectData.data) {
          this.projectData.data.currentChapter = newTitle;
        } else if (this.projectData) {
          this.projectData.currentChapter = newTitle;
        }
      } else {
        // 更新项目标题
        console.log('更新项目标题:', newTitle);
        this.localProjectTitle = newTitle;
        // 更新项目数据
        if (this.projectData) {
          this.projectData.title = newTitle;
        }
      }

      // 保存项目
      if (this.projectData) {
        this.saveProject(this.projectData);
      }
    },

    navigateToStep(stepName) {
      // 处理不同步骤的导航
      if (stepName === 'import') {
        // 如果点击"导入素材"，则返回CreationPage
        console.log('ContentCreationStudio: 点击导入素材，返回CreationPage');

        // 确保项目数据正确传递并且对项目数据进行深拷贝
        const projectDataToSend = JSON.parse(JSON.stringify(this.projectData));

        // 明确导航到creation页面，并传递项目数据
        this.$emit('navigate', 'creation', projectDataToSend);
      } else if (stepName === 'edit' || stepName === 'preview') {
        this.currentStep = stepName;
      }
    },

    // 强制刷新SRT内容的方法
    async forceRefreshSrtContent() {
      try {
        if (!this.projectData?.data?.srtFilePath) {
          this.showErrorMessage('无法刷新：未找到SRT文件路径');
          return false;
        }

        // 显示确认对话框
        const confirmMessage = `警告：强制刷新将使用最新的SRT文件内容替换当前所有字幕行，并丢失所有合并操作。确定要继续吗？`; // 更新确认消息
        if (!confirm(confirmMessage)) {
          console.log('用户取消了强制刷新');
          return false;
        }

        console.log('执行强制刷新，使用文件路径:', this.projectData.data.srtFilePath);
        this.isLoading = true; // 开始加载状态

        // 1. 读取SRT文件内容
        const srtContentResponse = await fetch(`/api/local/read-file?path=${encodeURIComponent(this.projectData.data.srtFilePath)}`);

        if (!srtContentResponse.ok) {
          console.error('无法读取SRT文件内容:', srtContentResponse.status);
          throw new Error(`无法读取SRT文件: ${srtContentResponse.status}`); // 抛出错误以便捕获
        }

        const srtResult = await srtContentResponse.json();

        if (!srtResult.success || !srtResult.content) {
          console.error('SRT文件内容无效');
          throw new Error('SRT文件内容无效'); // 抛出错误
        }

        // 2. 解析SRT内容
        console.log('开始解析SRT内容...');
        const subtitles = this.parseSrtContent(srtResult.content);

        if (!subtitles || subtitles.length === 0) {
          console.error('无法解析SRT内容或内容为空');
          throw new Error('无法解析SRT内容或内容为空'); // 抛出错误
        }
        console.log(`成功解析SRT，共 ${subtitles.length} 条字幕`);

        // 3. 创建全新的行数据，替换旧数据
        this.rows = subtitles.map((subtitle, index) => {
          // 创建行对象
          const row = {
            ...JSON.parse(JSON.stringify(this.emptyRow)), // 使用空行模板
            description: subtitle.text,
            startTime: subtitle.startTime,
            endTime: subtitle.endTime,
            duration: subtitle.duration,
            index: index + 1, // 显示序号，从1开始
            originalIndex: subtitle.index || (index + 1), // 原始序号
            originalState: { // 记录原始状态
              originalIndex: subtitle.index || (index + 1),
              index: index + 1,
              startTime: subtitle.startTime,
              endTime: subtitle.endTime,
              duration: subtitle.duration,
              description: subtitle.text
            },
            isMerged: false, // 强制重置为非合并状态
            mergedRows: [],  // 强制清空合并行
            tags: [] // 空标签数组，将来实现功能
          };

          return row;
        });
        console.log('已创建全新的行数据，行数:', this.rows.length);

        // 4. 更新项目数据并保存
        if (this.projectData.data) {
          this.projectData.data.rows = this.rows;
          this.projectData.data.srtContent = srtResult.content; // 保存新的SRT内容
          // 清除本地存储中的合并标志
          localStorage.removeItem(`project_${this.localProjectTitle}_${this.localChapterTitle}_hasMerged`);
          console.log('已清除合并状态标志');
          await this.saveProject(this.projectData); // 等待保存完成
        }

        this.isLoading = false; // 结束加载状态
        this.showSuccessMessage('强制刷新成功', `已加载 ${this.rows.length} 条新字幕`);
        return true; // 返回成功

      } catch (error) {
        console.error('强制刷新SRT内容时出错:', error);
        this.showErrorMessage('强制刷新失败', error.message);
        this.isLoading = false; // 确保错误时也结束加载
        return false; // 返回失败
      }
    },

    // 次级导航栏相关方法
    handleTabChange(tabId) {
      this.activeNavTab = tabId;
      console.log('切换到标签:', tabId);

      // 处理不同标签的操作
      if (tabId === 'adjust-shots') {
        console.log('ContentCreationStudio.vue - handleTabChange - 打开调整分镜抽屉，准备发送行数据，行数:', this.rows?.length);

        if (!this.rows || this.rows.length === 0) {
          console.error('ContentCreationStudio.vue - handleTabChange - 没有行数据可以发送');
          this.$emit('open-adjust-shots-drawer', []);
          return;
        }

        console.log('ContentCreationStudio.vue - handleTabChange - 发送的第一行数据示例:', JSON.stringify(this.rows[0], null, 2));

        // 确保行数据中包含所有必要信息，特别是合并相关的属性
        const rowsWithFullInfo = this.rows.map(row => {
          // 确保每行都有originalIndex
          if (row.originalIndex === undefined) {
            row.originalIndex = row.index;
          }

          // 确保每行都有isMerged属性
          if (row.isMerged === undefined) {
            row.isMerged = false;
          }

          // 确保合并行有mergedRows属性
          if (row.isMerged && !row.mergedRows) {
            row.mergedRows = [];
          }

          return row;
        });

        // 发出事件，传递处理后的行数据
        this.$emit('open-adjust-shots-drawer', rowsWithFullInfo);
      }
      else if (tabId === 'match-tags') {
        console.log('ContentCreationStudio.vue - handleTabChange - 打开全局推理抽屉，准备发送行数据，行数:', this.rows?.length);

        if (!this.rows || this.rows.length === 0) {
          console.error('ContentCreationStudio.vue - handleTabChange - 没有行数据可以发送');
          this.$emit('open-global-reasoning-drawer', []);
          return;
        }

        // 获取格式化的文本内容
        const formattedText = this.getFormattedTextForReasoning();
        console.log('ContentCreationStudio.vue - 生成的格式化文本预览:',
          formattedText.length > 200 ? formattedText.substring(0, 200) + '...' : formattedText);

        // 确保每行数据都有text属性
        const enhancedRows = this.rows.map(row => {
          // 如果row没有text属性，使用description填充
          if (!row.text && row.description) {
            return { ...row, text: row.description };
          }
          return row;
        });

        // 发出事件，传递处理后的行数据和格式化文本
        this.$emit('open-global-reasoning-drawer', enhancedRows, formattedText);
      }
    },

    handleNavAction(actionId) {
      console.log('执行操作:', actionId);

      // 处理不同的操作
      switch(actionId) {
        case 'smart-reasoning':
          this.performSmartReasoning();
          this.$refs.toast.show();
          break;
        case 'generate-images':
          this.generateImages();
          this.$refs.toast.show();
          break;
        case 'enlarge-images':
          this.enlargeImages();
          this.$refs.toast.show();
          break;
      }
    },

    // 行操作方法
    toggleRowSelection(index) {
      if (index >= 0 && index < this.rows.length) {
        this.rows[index].isSelected = !this.rows[index].isSelected;
      }
    },

    handleMergeUp(index) {
      if (index > 0) {
        // 使用mergeUp方法实现向上合并逻辑
        const result = this.mergeUp(index, this.rows);
        if (result.success) {
          // 更新行数据
          this.rows = result.rows;

          // 更新项目数据中的行并立即保存
          if (this.projectData.data) {
            // 更新项目数据中的行
            this.projectData.data.rows = this.rows;

            // 有合并操作时，记录到本地存储以便后续加载时保留状态
            const hasMerged = this.rows.some(row => row.isMerged);
            if (hasMerged) {
              const storageKey = `project_${this.localProjectTitle}_${this.localChapterTitle}_hasMerged`;
              localStorage.setItem(storageKey, 'true');
              console.log('记录合并状态:', storageKey, '=', 'true');
            }

            // 立即保存项目数据
            this.saveProject(this.projectData);
          }
        } else if (this.toastMessage) {
          // 只在失败且有消息时显示
          this.$refs.toast.show();
        }
      } else {
        this.showInfoMessage('提示', '无法合并第一行');
        this.$refs.toast.show();
      }
    },

    handleSplitDown(index) {
      // 实现向下分拆逻辑，使用splitDown方法
      const result = this.splitDown(index, this.rows);
      if (result.success) {
        // 更新行数据
        this.rows = result.rows;

        // 更新项目数据中的行并立即保存
        if (this.projectData.data) {
          // 更新项目数据中的行
          this.projectData.data.rows = this.rows;

          // 即使没有合并行也保留合并标志，以保持数据连续性
          const storageKey = `project_${this.localProjectTitle}_${this.localChapterTitle}_hasMerged`;
          localStorage.setItem(storageKey, 'true');

          // 立即保存项目数据
          this.saveProject(this.projectData);
        }
      } else if (this.toastMessage) {
        // 只在失败且有消息时显示
        this.$refs.toast.show();
      }
    },

    // 关键词更新
    updateKeywords(index, value) {
      if (index >= 0 && index < this.rows.length) {
        this.rows[index].keywords = value;
      }
    },

    // 图片操作
    handleSelectMainImage(index) {
      this.selectMainImage();
      this.showInfoMessage('功能提示', `正在处理第${index+1}行的图片选择...`);
      this.$refs.toast.show();
    },

    handleToggleImageLock(index) {
      if (index >= 0 && index < this.rows.length) {
        this.rows[index].isImageLocked = this.toggleImageLock(this.rows[index].isImageLocked);
        this.$refs.toast.show();
      }
    },

    handleClearMainImage(index) {
      if (index >= 0 && index < this.rows.length) {
        this.rows[index].imageSrc = this.clearMainImage();
        this.$refs.toast.show();
      }
    },

    // 缩略图操作
    handleSelectThumbnail(rowIndex, thumbnailIndex) {
      this.selectThumbnail(thumbnailIndex);
      this.showInfoMessage('功能提示', `已选择第${rowIndex+1}行的缩略图 ${thumbnailIndex + 1}`);
      this.$refs.toast.show();
    },

    handleManageImages(index) {
      this.manageImages();
      this.showInfoMessage('功能提示', `正在管理第${index+1}行的图片...`);
      this.$refs.toast.show();
    },

    // 操作列按钮
    handleRedrawImage(index) {
      this.redrawImage();
      this.showInfoMessage('重绘中', `正在重新生成第${index+1}行的镜头图片...`);
      this.$refs.toast.show();
    },

    handleInferPrompt(index) {
      this.inferPrompt();
      this.showInfoMessage('推理中', `正在推理第${index+1}行的提示词...`);
      this.$refs.toast.show();
    },

    // 添加直接从SRT文件加载内容的方法
    async forceReadSrtFile() {
      if (!this.projectData || !this.projectData.data || !this.projectData.data.srtFilePath) {
        this.showErrorMessage('项目数据中没有SRT文件路径');
        return false;
      }

      const srtPath = this.projectData.data.srtFilePath;
      console.log('尝试直接读取SRT文件:', srtPath);

      try {
        // 使用/api/local/read-file API读取文件
        const apiUrl = `/api/local/read-file?path=${encodeURIComponent(srtPath)}`;
        console.log('发送读取SRT文件请求到:', apiUrl);

        const response = await fetch(apiUrl);

        if (!response.ok) {
          console.error('读取SRT文件失败:', response.status, response.statusText);
          this.showErrorMessage(`读取SRT文件失败: ${response.status}`);
          return false;
        }

        const result = await response.json();

        if (!result.success) {
          console.error('读取SRT文件失败:', result.error);
          this.showErrorMessage(`读取SRT文件失败: ${result.error}`);
          return false;
        }

        // 解析SRT内容
        const srtContent = result.content;
        console.log('成功读取SRT文件内容，长度:', srtContent.length);

        if (!srtContent || srtContent.trim().length === 0) {
          console.error('SRT文件内容为空');
          this.showErrorMessage('SRT文件内容为空');
          return false;
        }

        // 解析SRT内容，创建行数据
        const parsedRows = this.parseSrtContent(srtContent);
        console.log('解析后的行数据数量:', parsedRows.length);

        if (parsedRows.length === 0) {
          console.error('SRT解析结果为空');
          this.showErrorMessage('SRT解析结果为空，可能格式不正确');
          return false;
        }

        // 设置行数据
        this.rows = parsedRows;
        console.log('行数据设置成功，总行数:', this.rows.length);

        // 更新项目数据中的行数据
        if (!this.projectData.data) {
          this.projectData.data = {};
        }
        this.projectData.data.rows = this.rows;

        // 设置加载状态
        this.isLoading = false;

        // 保存项目数据到文件
        await this.saveProjectDataToFile();

        return true;
      } catch (error) {
        console.error('强制读取SRT文件失败:', error);
        this.showErrorMessage(`强制读取SRT文件失败: ${error.message}`);
        return false;
      }
    },

    // 强制初始化数据
    async forceInitializeData() {
      console.log('强制初始化数据...');

      if (!this.projectData || !this.projectData.data) {
        this.showErrorMessage('项目数据不完整');
        return false;
      }

      // 检查是否有SRT文件路径
      if (!this.projectData.data.srtFilePath) {
        console.error('项目数据中没有SRT文件路径');
        this.showErrorMessage('项目数据中没有SRT文件路径，请先上传SRT文件');
        return false;
      }

      // 直接从SRT文件加载内容
      const success = await this.forceReadSrtFile();

      if (success) {
        console.log('成功强制加载SRT文件内容并初始化数据');
        this.showSuccessMessage('数据加载成功', `成功加载 ${this.rows.length} 条字幕`);
        return true;
      } else {
        console.error('强制初始化数据失败');
        this.useTestData();
        return false;
      }
    },

    // 保存项目数据到文件
    async saveProjectDataToFile() {
      try {
        console.log('保存项目数据到文件...');

        if (!this.projectData || !this.projectData.data) {
          console.error('项目数据不完整，无法保存');
          return false;
        }

        if (!this.localProjectTitle || !this.localChapterTitle) {
          console.error('项目标题或章节标题缺失，无法保存');
          return false;
        }

        // 构建保存数据
        const saveData = {
          projectTitle: this.localProjectTitle,
          chapterTitle: this.localChapterTitle,
          data: this.projectData.data
        };

        // 发送保存请求
        const response = await fetch('/api/local/save-project-data', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(saveData)
        });

        if (!response.ok) {
          console.error('保存项目数据失败:', response.status, response.statusText);
          return false;
        }

        const result = await response.json();

        if (!result.success) {
          console.error('保存项目数据失败:', result.error || '未知错误');
          return false;
        }

        console.log('项目数据保存成功:', result);
        return true;
      } catch (error) {
        console.error('保存项目数据时出错:', error);
        return false;
      }
    },

    // 添加自动检测和处理SRT文件变更的方法
    async autoDetectSrtChanges() {
      if (!this.localProjectTitle || !this.localChapterTitle) {
        console.warn('无法检测SRT变更：缺少项目或章节信息');
        return false;
      }

      try {
        // 检查是否有更新标志
        const updateKey = `srt_updated_${this.localProjectTitle}_${this.localChapterTitle}`;
        const updateTimestamp = localStorage.getItem(updateKey);

        if (updateTimestamp) {
          console.log('检测到SRT更新标志，时间戳:', updateTimestamp);
          // 移除标志，避免重复处理
          localStorage.removeItem(updateKey);

          // 获取当前目录中的文件列表
          const filesPath = `draft/${this.localProjectTitle}/${this.localChapterTitle}`;
          const filesResponse = await fetch(`/api/local/list-files?path=${encodeURIComponent(filesPath)}`);

          if (!filesResponse.ok) {
            console.error('无法获取目录文件列表');
            return false;
          }

          const files = await filesResponse.json();
          // 查找SRT文件
          const srtFiles = files.filter(file => file.toLowerCase().endsWith('.srt'));

          if (srtFiles.length === 0) {
            console.warn('目录中未找到SRT文件，但有更新标志');
            return false;
          }

          // 如果有多个SRT文件，选择最新的一个
          srtFiles.sort();
          const latestSrtFile = srtFiles[srtFiles.length - 1];

          // 构建SRT文件完整路径
          const srtPath = `${filesPath}/${latestSrtFile}`;
          console.log('发现更新的SRT文件:', srtPath);

          // 刷新SRT内容（总是加载新内容）
          const result = await this.refreshSrtContent(srtPath, true);
          return result;
        }

        // 如果没有更新标志，检查文件变更
        const filesPath = `draft/${this.localProjectTitle}/${this.localChapterTitle}`;
        console.log('检查目录中的SRT文件:', filesPath);
        const filesResponse = await fetch(`/api/local/list-files?path=${encodeURIComponent(filesPath)}`);

        if (!filesResponse.ok) {
          console.error('无法获取目录文件列表');
          return false;
        }

        const files = await filesResponse.json();
        // 查找SRT文件
        const srtFiles = files.filter(file => file.toLowerCase().endsWith('.srt'));

        if (srtFiles.length === 0) {
          console.warn('目录中未找到SRT文件');
          return false;
        }

        // 如果有多个SRT文件，选择最新的一个
        srtFiles.sort();
        const latestSrtFile = srtFiles[srtFiles.length - 1];

        // 构建SRT文件完整路径
        const srtPath = `${filesPath}/${latestSrtFile}`;
        console.log('发现SRT文件:', srtPath);

        // 检查是否与当前加载的文件路径相同
        if (this.projectData?.data?.srtFilePath === srtPath) {
          console.log('当前已加载此SRT文件，无需刷新');
          return false;
        }

        console.log('发现新的SRT文件路径，准备刷新:', srtPath);

        // 刷新SRT内容
        const result = await this.refreshSrtContent(srtPath, true);
        return result;
      } catch (error) {
        console.error('自动检测SRT变更出错:', error);
        return false;
      }
    },

    // 添加refreshSrtContent方法，用于主动刷新SRT内容
    async refreshSrtContent(newSrtPath, forceRefresh = false) {
      if (!newSrtPath) {
        console.warn('没有提供SRT文件路径，无法刷新内容');
        return false;
      }

      try {
        console.log('开始刷新SRT内容，路径:', newSrtPath);

        // 读取SRT文件内容
        const srtContentResponse = await fetch(`/api/local/read-file?path=${encodeURIComponent(newSrtPath)}`);

        if (!srtContentResponse.ok) {
          console.error('无法读取SRT文件内容:', srtContentResponse.status);
          this.showErrorMessage('无法读取SRT文件内容');
          return false;
        }

        const srtResult = await srtContentResponse.json();

        if (!srtResult.success || !srtResult.content) {
          console.error('SRT文件内容无效');
          this.showErrorMessage('SRT文件内容无效');
          return false;
        }

        console.log('成功读取SRT文件内容，长度:', srtResult.content.length);

        // 解析SRT内容
        const parsedSubtitles = this.parseSrtContent(srtResult.content);
        console.log(`成功解析SRT内容，共${parsedSubtitles.length}条字幕`);

        // 如果解析结果为空，提示错误
        if (parsedSubtitles.length === 0) {
          console.error('SRT解析结果为空，可能格式不正确');
          this.showErrorMessage('SRT解析结果为空，可能格式不正确');
          return false;
        }

        // 更新项目数据
        if (!this.projectData.data) {
          this.projectData.data = {};
        }

        // 保存SRT内容和路径
        this.projectData.data.srtContent = srtResult.content;
        this.projectData.data.srtFilePath = newSrtPath;

        // 提取文件名
        const pathParts = newSrtPath.split('/');
        const srtFileName = pathParts[pathParts.length - 1];
        this.projectData.data.srtFile = srtFileName;

        // 转换为行数据，传递forceRefresh参数
        this.convertSubtitlesToRows(parsedSubtitles, forceRefresh);

        // 更新文件状态
        this.hasSrtFile = true;

        // 如果之前有合并操作，清除合并标记
        if (this.rows.some(row => row.isMerged)) {
          localStorage.removeItem(`project_${this.localProjectTitle}_${this.localChapterTitle}_hasMerged`);
        }

        // 保存项目数据
        await this.saveProject(this.projectData);

        // 显示成功消息
        this.showSuccessMessage('SRT内容已更新', `成功加载 ${parsedSubtitles.length} 条字幕`);

        return true;
      } catch (error) {
        console.error('刷新SRT内容出错:', error);
        this.showErrorMessage('刷新SRT内容失败: ' + error.message);
        return false;
      }
    },

    // 提供一个公共方法，可以被父组件调用来触发刷新
    checkForSrtUpdates() {
      // 检查项目数据是否发生变化
      if (!this.projectData || !this.projectData.data || !this.projectData.data.srtFilePath) {
        return false;
      }

      // 直接使用当前项目中记录的SRT文件路径刷新内容
      return this.refreshSrtContent(this.projectData.data.srtFilePath);
    },

    // 添加行
    addRow() {
      this.rows.push(JSON.parse(JSON.stringify(this.emptyRow)));
    },

    // 处理导出/导入事件 (占位)
    handleExportImport() {
      console.log('handleExportImport 方法被调用，但尚未实现具体功能。');
      this.showToast('info', '功能未实现', '导出/导入功能正在开发中。');
    },

    // 显示Toast通知
    showToast(type, title, message) {
      this.toastType = type;
      this.toastTitle = title;
      this.toastMessage = message;
      this.$nextTick(() => {
        this.$refs.toast.$refs.toastElement.classList.add('show');
      });
    },

    // 接收并处理从AdjustShotsDrawer返回的行数据
    updateRowsFromDrawer(updatedRows) {
      console.log('从AdjustShotsDrawer接收到更新的行数据，行数:', updatedRows.length);

      // 检查是否有合并行
      const mergedRows = updatedRows.filter(row => row.isMerged);
      console.log('合并行数量:', mergedRows.length);

      if (mergedRows.length > 0) {
        console.log('合并行详情:', mergedRows.map(row => ({
          index: row.index,
          isMerged: row.isMerged,
          mergedRowsCount: row.mergedRows?.length || 0
        })));

        // 设置合并标志到localStorage
        localStorage.setItem(`project_${this.localProjectTitle}_${this.localChapterTitle}_hasMerged`, 'true');
      }

      // 更新行数据
      this.rows = updatedRows;

      // 更新项目数据中的行
      if (this.projectData.data) {
        this.projectData.data.rows = this.rows;

        // 立即保存项目数据到文件
        console.log('保存更新后的项目数据到project.json...');
        this.saveProject(this.projectData).then(() => {
          console.log('项目数据保存成功，包含合并状态');
        }).catch(error => {
          console.error('保存项目数据失败:', error);
        });
      }
    },

    // 新增方法：获取格式化的文本内容供全局推理使用
    getFormattedTextForReasoning() {
      if (!this.rows || this.rows.length === 0) {
        return '';
      }

      // 创建一个连续的纯文本表示，不包含镜头编号等标识
      const formattedText = this.rows.map((row) => {
        // 合并行需要特殊处理
        if (row.isMerged && row.mergedRows && row.mergedRows.length > 0) {
          // 包含合并行的信息，但不添加标识
          const mergedTexts = row.mergedRows.map(mr => mr.description || '').filter(t => t.trim());
          const combinedText = mergedTexts.join('\n');
          return row.description || combinedText;
        }

        // 只返回描述文本，不添加镜头编号等标识
        let fullText = row.description || '';

        return fullText;
      }).join('\n\n');

      return formattedText;
    },
  },
  watch: {
    // 监听项目数据变化并更新上下文
    projectData: {
      handler(newProjectData) {
        console.log('[ContentCreationStudio] 项目数据变化，设置到上下文:', newProjectData);
        if (newProjectData && this.projectContextTools) {
          console.log('[ContentCreationStudio] 行数据数量:', newProjectData?.data?.rows?.length || 0);
          this.projectContextTools.setProjectData(newProjectData);
          this.projectContextTools.setProjectTitle(newProjectData.title || '');
          this.projectContextTools.setChapterTitle(newProjectData.currentChapter || '');
        }
      },
      deep: true,
      immediate: true
    },

    // 监听当前步骤变化
    currentStep(newStep) {
      if (newStep && this.projectContextTools) {
        this.projectContextTools.setCurrentStep(newStep);
      }
    },
  }
}
</script>

<style scoped>
@import '../assets/styles/studio.css';

/* 添加无数据时的提示样式 */
.no-data-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 20px;
  background-color: #1a1a1a;
  border-radius: 8px;
  margin: 20px;
  text-align: center;
}

.message-icon {
  font-size: 40px;
  margin-bottom: 20px;
  color: #2dc0f0;
}

.message-text {
  font-size: 18px;
  color: #e0e0e0;
  margin-bottom: 10px;
}

.message-subtext {
  font-size: 14px;
  color: #999;
  margin-bottom: 10px;
}

/* 添加手动加载按钮样式 */
.force-load-button {
  margin-top: 15px;
  padding: 8px 15px;
  background-color: #2dc0f0;
  color: #121212;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
}

.force-load-button:hover {
  background-color: #1aa8d6;
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(45, 192, 240, 0.4);
}

.force-load-button:active {
  transform: translateY(0);
  background-color: #1590ba;
}

.icon-info::before {
  content: "i";
  display: inline-block;
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 50%;
  background-color: #2dc0f0;
  color: #121212;
  font-style: italic;
  font-weight: bold;
}

.icon-warning::before {
  content: "!";
  display: inline-block;
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 50%;
  background-color: #ff9800;
  color: #121212;
  font-style: italic;
  font-weight: bold;
}

.icon-loading::before {
  content: "";
  display: inline-block;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 4px solid rgba(45, 192, 240, 0.3);
  border-top-color: #2dc0f0;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 添加操作按钮样式 */
.action-buttons {
  display: flex;
  justify-content: flex-end;
  padding: 10px 20px;
  background-color: #1d1d2c;
  border-bottom: 1px solid #333355;
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #2d94cc;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background-color: #3da3d9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(45, 148, 204, 0.3);
}

.refresh-btn:active {
  transform: translateY(0);
  background-color: #2280b3;
}

.refresh-btn i {
  font-size: 1.1rem;
}
</style>