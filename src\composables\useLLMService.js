import { ref } from 'vue';

/**
 * LLM服务组合式函数，提供与大型语言模型通信的通用接口
 */
export function useLLMService() {
  // 状态管理
  const isLoading = ref(false);
  const error = ref(null);
  const responseText = ref('');
  const responseData = ref(null);
  const statusMessage = ref('');
  const llmConfig = ref(null); // <--- 确保顶层 llmConfig 是一个 ref

  // 从服务器获取LLM配置
  async function fetchLLMConfigInternal() { // Renamed to avoid conflict if exported, and to signify internal use
    try {
      console.log('[useLLMService] Fetching LLM config from API /api/local/load-user-settings?filename=usersettings.json');
      const response = await fetch('/api/local/load-user-settings?filename=usersettings.json');

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`获取LLM配置失败: ${response.status} ${response.statusText}. Response: ${errorText}`);
      }

      const result = await response.json();

      if (result.success && result.settings && result.settings.llm) {
        console.log('[useLLMService] LLM Config loaded successfully from API:', result.settings.llm);
        llmConfig.value = result.settings.llm; // <--- 赋值给顶层的 ref
        return llmConfig.value;
      } else {
        throw new Error(result.error || '从API获取的LLM配置格式不正确或缺失llm字段');
      }
    } catch (err) {
      console.error('获取LLM配置出错:', err);
      error.value = '无法加载LLM配置';
      llmConfig.value = null; // <--- 确保出错时也清空
      return null;
    }
  }

  /**
   * 与本地API通信
   * @param {string} text - 要发送的文本
   * @param {string} prompt - 提示词
   * @param {Object} options - 额外选项
   */
  async function callLocalAPI(text, prompt, options = {}) {
    statusMessage.value = '正在调用本地API...';
    console.log('[useLLMService] callLocalAPI invoked. AnalyzeType:', options.analyzeType, 'Text (first 50 chars):', typeof text === 'string' ? text.substring(0,50) : typeof text);

    if (!llmConfig.value) {
      await fetchLLMConfigInternal();
    }

    if (!llmConfig.value || !llmConfig.value.localUrl) {
      console.error('[useLLMService] callLocalAPI: Local URL in LLM config is missing.');
      throw new Error('本地API的URL配置缺失。');
    }

    const requestBody = {
      text: text,
      analyzeType: options.analyzeType || 'general',
    };

    if (prompt) {
      requestBody.prompt = prompt;
      console.log('[useLLMService] callLocalAPI: Adding user-defined prompt to request body.');
    }

    console.log('[useLLMService] callLocalAPI - Request body keys:', Object.keys(requestBody), 'Prompt length:', requestBody.prompt ? requestBody.prompt.length : 'N/A', 'Text length:', requestBody.text ? requestBody.text.length : 'N/A');

    try {
      const response = await fetch(`${llmConfig.value.localUrl}/api/local/analyze-text`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`本地API请求失败: ${response.status}. 响应: ${errorText}`);
      }

      const result = await response.json();
      responseData.value = result.data;
      responseText.value = result.text;

      console.log('[useLLMService] Local API 响应成功。Data:',
                  result.data ? '有结构化数据' : '无结构化数据',
                  'Text 长度:', result.text ? result.text.length : 0);

      return result;
    } catch (error) {
      console.error('[useLLMService] 本地API调用出错:', error);
      statusMessage.value = `本地API调用出错: ${error.message}`;
      error.value = error.message;
      throw error;
    }
  }

  /**
   * 调用远程LLM API
   * @param {string} prompt - 提示词
   * @param {Object} config - LLM配置
   */
  async function callRemoteAPI(prompt, config) {
    let apiUrl, headers, requestBody;

    switch(config.provider) {
      case 'google':
        apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${config.defaultModel}:generateContent?key=${config.googleApiKey}`;
        headers = { 'Content-Type': 'application/json' };
        requestBody = {
          contents: [{ parts: [{ text: prompt }] }],
          generationConfig: {
            temperature: config.temperature || 0.7,
          }
        };
        break;

      case 'openrouter':
        apiUrl = 'https://openrouter.ai/api/v1/chat/completions';
        headers = {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.openrouterApiKey}`,
          'HTTP-Referer': window.location.origin,
          'X-Title': 'txt2video'
        };
        requestBody = {
          model: config.defaultModel || 'openai/gpt-3.5-turbo',
          messages: [{ role: 'user', content: prompt }],
          temperature: config.temperature || 0.7,
        };
        break;

      case 'openai':
        apiUrl = 'https://api.openai.com/v1/chat/completions';
        headers = {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.openaiApiKey}`
        };
        requestBody = {
          model: config.defaultModel || 'gpt-3.5-turbo',
          messages: [{ role: 'user', content: prompt }],
          temperature: config.temperature || 0.7,
        };
        break;

      case 'anthropic':
        apiUrl = 'https://api.anthropic.com/v1/messages';
        headers = {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.anthropicApiKey}`,
          'anthropic-version': '2023-06-01'
        };
        requestBody = {
          model: config.defaultModel || 'claude-3-haiku-20240307',
          max_tokens: 4000,
          messages: [{ role: 'user', content: prompt }],
          temperature: config.temperature || 0.7,
        };
        break;

      default:
        throw new Error('不支持的LLM提供商');
    }

    statusMessage.value = `正在使用${config.provider}处理...`;
    console.log(`调用${config.provider} API: ${apiUrl}`);

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`${config.provider} API请求失败: ${response.status}. 响应: ${errorText}`);
    }

    return await response.json();
  }

  /**
   * 从不同提供商的响应中提取文本
   */
  function extractTextFromResponse(data, provider) {
    let extractedText = '';

    if (provider === 'google') {
      if (data.candidates && data.candidates[0]?.content?.parts?.[0]) {
        extractedText = data.candidates[0].content.parts[0].text;
      } else {
        throw new Error('无法从Google API响应中提取文本');
      }
    } else if (provider === 'anthropic') {
      if (data.content?.[0]?.text) {
        extractedText = data.content[0].text;
      } else {
        throw new Error('无法从Anthropic API响应中提取文本');
      }
    } else { // OpenAI, OpenRouter, Local
      if (data.choices?.[0]?.message?.content) {
        extractedText = data.choices[0].message.content;
      } else {
        throw new Error(`无法从${provider} API响应中提取文本`);
      }
    }

    return extractedText;
  }

  /**
   * 尝试将文本解析为JSON
   */
  function tryParseJSON(text) {
    try {
      // 清理Markdown代码块标记
      const cleanedText = text
        .replace(/```json\n/g, '')
        .replace(/```json/g, '')
        .replace(/```\n/g, '')
        .replace(/\n```/g, '')
        .replace(/```/g, '')
        .trim();
      // 新增：仅以 { 或 [ 开头才尝试解析
      if (!cleanedText.startsWith('{') && !cleanedText.startsWith('[')) {
        return {
          success: false,
          data: null,
          error: 'Not JSON',
          rawText: text
        };
      }
      return {
        success: true,
        data: JSON.parse(cleanedText)
      };
    } catch (err) {
      console.error('JSON解析失败:', err);
      return {
        success: false,
        data: null,
        error: err.message,
        rawText: text
      };
    }
  }

  /**
   * 发送文本到LLM进行处理
   * @param {string} text - 要处理的文本内容
   * @param {string} promptTemplate - 提示词模板
   * @param {Object} options - 选项
   */
  async function sendTextToLLM(originalText, fullPrompt, options = {}) {
    isLoading.value = true;
    error.value = null;
    statusMessage.value = '准备发送文本到LLM...';
    responseData.value = null;
    responseText.value = '';

    console.log('[useLLMService] sendTextToLLM called with:', {
      originalTextLength: originalText?.length || 0,
      fullPromptLength: fullPrompt?.length || 0,
      options
    });

    try {
      // 每次都强制从服务器拉取最新 LLM 配置，避免缓存导致设置不生效
      await fetchLLMConfigInternal();
      // 现在 llmConfig.value 应该是最新的配置或 null (如果加载失败)
      if (!llmConfig.value || !llmConfig.value.provider) {
        throw new Error('LLM配置不完整或加载失败。请先配置LLM服务。');
      }
      console.log('[useLLMService] 实际使用的 LLM 配置:', llmConfig.value);
      const currentLLMConfig = llmConfig.value; // Use the ref's value
      const { provider } = currentLLMConfig;
      statusMessage.value = `使用 ${provider} 提供的LLM...`;

      // 首先尝试通过本地API服务调用
      if (!options.skipLocalAPI) {
        try {
          statusMessage.value = '尝试通过本地API处理...';
          const localResult = await callLocalAPI(fullPrompt, originalText, options);
          return localResult;
        } catch (localError) {
          console.warn('本地API处理失败，将尝试远程LLM:', localError);
          statusMessage.value = '本地API处理失败，尝试远程LLM...';
          // 如果本地API调用明确地因为缺少 localUrl 而失败，或者我们想强制远程，则不应在此处中止
          if (localError.message.includes('本地API的URL配置缺失')) {
             console.log('[useLLMService] 本地API URL缺失，继续尝试远程调用。');
          } else if (options.forceRemote) {
             console.log('[useLLMService] 强制远程调用。');
          } else {
            // 对于其他本地API错误，可能我们不希望继续尝试远程
            // 或者，如果策略是总是尝试远程作为后备，则移除此 else if/else
            // throw localError; // 如果本地API失败则不再尝试远程 (除非是URL缺失或强制远程)
          }
        }
      }

      // 如果本地API失败或选择跳过，则调用远程LLM API
      statusMessage.value = `准备调用远程 ${provider} API...`;

      // 确定要尝试的提供商顺序 (如果需要更复杂的故障转移逻辑)
      // 目前，如果 skipLocalAPI 为 false，则意味着本地已尝试（可能成功或失败）
      // 如果本地失败，我们会落到这里，或者如果 skipLocalAPI 为 true
      // 这里的 'provider' 是从 llmConfig.value 中获取的，应优先使用它

      const providersToTry = [];
      if (currentLLMConfig.provider && currentLLMConfig[`${currentLLMConfig.provider}ApiKey`]) {
        providersToTry.push(currentLLMConfig.provider);
      }
      // 可以添加其他后备提供商，如果需要
      // if (currentLLMConfig.openrouterApiKey && currentLLMConfig.provider !== 'openrouter') {
      //   providersToTry.push('openrouter');
      // }

      if (providersToTry.length === 0) {
         // 检查是否有任何一个API Key存在，即使不是主provider
        if (currentLLMConfig.googleApiKey) providersToTry.push('google');
        else if (currentLLMConfig.openrouterApiKey) providersToTry.push('openrouter');
        else if (currentLLMConfig.openaiApiKey) providersToTry.push('openai');
        else if (currentLLMConfig.anthropicApiKey) providersToTry.push('anthropic');

        if (providersToTry.length === 0) {
            throw new Error('未配置任何有效的LLM提供商API密钥。');
        }
         console.warn(`主提供商 ${currentLLMConfig.provider} 可能未配置API密钥，尝试其他可用提供商: ${providersToTry.join(', ')}`);
      }

      let lastError = null;
      for (const attemptProvider of providersToTry) {
        try {
          // 使用 currentLLMConfig，但确保我们正在尝试的 provider 有 key
          const configForAttempt = { ...currentLLMConfig, provider: attemptProvider };
          if (!configForAttempt[`${attemptProvider}ApiKey`]) {
            console.warn(`尝试 ${attemptProvider} 但缺少API密钥，跳过。`);
            continue;
          }

          const remoteApiResponse = await callRemoteAPI(fullPrompt, configForAttempt);

          const extractedText = extractTextFromResponse(remoteApiResponse, attemptProvider);
          responseText.value = extractedText;

          const parseResult = tryParseJSON(extractedText);
          if (parseResult.success) {
            responseData.value = parseResult.data;
          } else {
            // 如果解析失败，responseData 保持为 null，或根据需求设置错误
             console.warn(`[useLLMService] 远程API (${attemptProvider}) 响应JSON解析失败:`, parseResult.error);
          }

          return {
            success: true,
            text: responseText.value,
            data: responseData.value,
            originalResponse: remoteApiResponse
          };
        } catch (providerErr) {
          console.error(`使用 ${attemptProvider} 提供商进行远程调用出错:`, providerErr);
          lastError = providerErr;
        }
      }

      if (lastError) {
        throw lastError;
      } else {
        throw new Error('所有尝试的远程LLM提供商都失败，但没有详细错误信息。');
      }
    } catch (err) {
      console.error('LLM处理出错:', err);
      error.value = err.message;
      return {
        success: false,
        error: err.message,
        text: responseText.value,
        data: responseData.value
      };
    } finally {
      isLoading.value = false;
      statusMessage.value = '';
    }
  }

  /**
   * 使用自定义提示词进行AI处理
   */
  async function processWithCustomPrompt(text, customPrompt, options = {}) {
    return await sendTextToLLM(text, customPrompt, options);
  }

  /**
   * 独立的LLM调用函数，不影响全局isLoading状态
   * 用于智能特名、智能场景等功能
   */
  async function sendTextToLLMIndependent(originalText, fullPrompt, options = {}) {
    // 不设置全局的isLoading状态
    const localError = ref(null);
    const localResponseText = ref('');
    const localResponseData = ref(null);
    const localStatusMessage = ref('');

    localStatusMessage.value = '准备发送文本到LLM...';

    console.log('[useLLMService] sendTextToLLMIndependent called with:', {
      originalTextLength: originalText?.length || 0,
      fullPromptLength: fullPrompt?.length || 0,
      options
    });

    try {
      // 每次都强制从服务器拉取最新 LLM 配置，避免缓存导致设置不生效
      await fetchLLMConfigInternal();
      // 现在 llmConfig.value 应该是最新的配置或 null (如果加载失败)
      if (!llmConfig.value || !llmConfig.value.provider) {
        throw new Error('LLM配置不完整或加载失败。请先配置LLM服务。');
      }
      console.log('[useLLMService] 实际使用的 LLM 配置:', llmConfig.value);
      const currentLLMConfig = llmConfig.value; // Use the ref's value
      const { provider } = currentLLMConfig;
      localStatusMessage.value = `使用 ${provider} 提供的LLM...`;

      // 首先尝试通过本地API服务调用
      if (!options.skipLocalAPI) {
        try {
          localStatusMessage.value = '尝试通过本地API处理...';
          const localResult = await callLocalAPI(fullPrompt, originalText, options);
          return localResult;
        } catch (localAPIError) {
          console.warn('本地API处理失败，将尝试远程LLM:', localAPIError);
          localStatusMessage.value = '本地API处理失败，尝试远程LLM...';
          // 如果本地API调用明确地因为缺少 localUrl 而失败，或者我们想强制远程，则不应在此处中止
          if (localAPIError.message.includes('本地API的URL配置缺失')) {
             console.log('[useLLMService] 本地API URL缺失，继续尝试远程调用。');
          } else if (options.forceRemote) {
             console.log('[useLLMService] 强制远程调用。');
          }
        }
      }

      // 如果本地API失败或选择跳过，则调用远程LLM API
      localStatusMessage.value = `准备调用远程 ${provider} API...`;

      const providersToTry = [];
      if (currentLLMConfig.provider && currentLLMConfig[`${currentLLMConfig.provider}ApiKey`]) {
        providersToTry.push(currentLLMConfig.provider);
      }

      if (providersToTry.length === 0) {
         // 检查是否有任何一个API Key存在，即使不是主provider
        if (currentLLMConfig.googleApiKey) providersToTry.push('google');
        else if (currentLLMConfig.openrouterApiKey) providersToTry.push('openrouter');
        else if (currentLLMConfig.openaiApiKey) providersToTry.push('openai');
        else if (currentLLMConfig.anthropicApiKey) providersToTry.push('anthropic');

        if (providersToTry.length === 0) {
            throw new Error('未配置任何有效的LLM提供商API密钥。');
        }
         console.warn(`主提供商 ${currentLLMConfig.provider} 可能未配置API密钥，尝试其他可用提供商: ${providersToTry.join(', ')}`);
      }

      let lastError = null;
      for (const attemptProvider of providersToTry) {
        try {
          // 使用 currentLLMConfig，但确保我们正在尝试的 provider 有 key
          const configForAttempt = { ...currentLLMConfig, provider: attemptProvider };
          if (!configForAttempt[`${attemptProvider}ApiKey`]) {
            console.warn(`尝试 ${attemptProvider} 但缺少API密钥，跳过。`);
            continue;
          }

          const remoteApiResponse = await callRemoteAPI(fullPrompt, configForAttempt);

          const extractedText = extractTextFromResponse(remoteApiResponse, attemptProvider);
          localResponseText.value = extractedText;

          const parseResult = tryParseJSON(extractedText);
          if (parseResult.success) {
            localResponseData.value = parseResult.data;
          } else {
            // 如果解析失败，responseData 保持为 null，或根据需求设置错误
             console.warn(`[useLLMService] 远程API (${attemptProvider}) 响应JSON解析失败:`, parseResult.error);
          }

          return {
            success: true,
            text: localResponseText.value,
            data: localResponseData.value,
            originalResponse: remoteApiResponse
          };
        } catch (providerErr) {
          console.error(`使用 ${attemptProvider} 提供商进行远程调用出错:`, providerErr);
          lastError = providerErr;
        }
      }

      if (lastError) {
        throw lastError;
      } else {
        throw new Error('所有尝试的远程LLM提供商都失败，但没有详细错误信息。');
      }
    } catch (err) {
      console.error('LLM处理出错:', err);
      localError.value = err.message;
      return {
        success: false,
        error: err.message,
        text: localResponseText.value,
        data: localResponseData.value
      };
    } finally {
      // 不设置全局的isLoading状态
      localStatusMessage.value = '';
    }
  }

  return {
    isLoading,
    error,
    responseText,
    responseData,
    statusMessage,
    sendTextToLLM,
    sendTextToLLMIndependent,
    processWithCustomPrompt,
    callLocalAPI,
    fetchLLMConfig: fetchLLMConfigInternal // Expose the internal renamed one if needed by other parts
  };
}