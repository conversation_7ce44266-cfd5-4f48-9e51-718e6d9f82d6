// 其他API端点（如果需要）

const express = require('express');
const fs = require('fs');
const path = require('path');
const os = require('os');

module.exports = function(app) {
  // 可以在这里添加其他API，但不要与localCommunication.js中的路由冲突
  // 注释掉所有与localCommunication.js中重复的路由
  
  // 示例路由
  app.get('/api/info', (req, res) => {
    res.json({
      version: '1.0.0',
      serverTime: new Date().toISOString()
    });
  });
};
