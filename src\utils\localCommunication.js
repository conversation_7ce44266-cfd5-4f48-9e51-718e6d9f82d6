import { ref } from 'vue';

// 统一的错误处理
async function handleResponse(response) {
  try {
  const data = await response.json();
  
  // 如果返回的是数组，说明是列表接口
  if (Array.isArray(data)) {
    return data;
  }
  
  // 如果返回的是对象，检查 success 字段
  if (!response.ok || (data && !data.success)) {
      console.error('API响应错误:', { status: response.status, data });
      throw new Error(data.error || `操作失败 (${response.status})`);
  }
  
  return data;
  } catch (error) {
    console.error('解析API响应失败:', error);
    if (error.name === 'SyntaxError') {
      // 尝试获取原始文本进行诊断
      const text = await response.text().catch(() => '无法获取响应内容');
      console.error('API响应不是有效的JSON:', text.slice(0, 100) + (text.length > 100 ? '...' : ''));
      throw new Error('无效的服务器响应');
    }
    throw error;
  }
}

// 统一的 API 调用函数
async function callApi(endpoint, method = 'GET', body = null) {
  const options = {
    method,
    headers: body ? { 'Content-Type': 'application/json' } : undefined,
    body: body ? JSON.stringify(body) : undefined
  };

  try {
    console.log(`API请求: ${method} /api/local/${endpoint}`, body ? body : '');
    const res = await fetch(`/api/local/${endpoint}`, options);
    return handleResponse(res);
  } catch (error) {
    console.error(`API调用失败 (${endpoint}):`, error);
    throw error;
  }
}

// 导出 Composition API 方式的工具函数
export function useLocalCommunication() {
  const loading = ref(false);
  const error = ref(null);

  // 统一的加载状态处理
  async function withLoading(operation) {
    loading.value = true;
    error.value = null;
    try {
      const result = await operation();
      return result;
    } catch (e) {
      error.value = e.message;
      throw e;
    } finally {
      loading.value = false;
    }
  }

  // 文件夹操作函数
  const api = {
    // 创建文件夹
    async createFolder(folderName, base = '') {
      return withLoading(() => 
        callApi('create-folder', 'POST', { folderName, base })
      );
    },

    // 重命名文件夹
    async renameFolder(oldName, newName, base = '') {
      console.log(`重命名文件夹: ${oldName} -> ${newName}, 项目目录: ${base}`);
      // 关键是传递 base 参数，指定重命名的上下文
      return withLoading(() => 
        callApi('rename-folder', 'POST', { oldName, newName, base })
      );
    },

    // 删除文件夹
    async deleteFolder(folderName, base = '') {
      return withLoading(() => 
        callApi('delete-folder', 'POST', { folderName, base })
      );
    },

    // 获取文件夹列表
    async listFolders(base = '') {
      const query = base ? `list-folders?base=${encodeURIComponent(base)}` : 'list-folders';
      return withLoading(() => callApi(query));
    },

    // 保存项目文件（如 Current.json）
    async saveProjectFile(projectTitle, fileName, content) {
      return withLoading(() =>
        callApi('save-project-file', 'POST', { projectTitle, fileName, content })
      );
    },

    // 获取默认 prompt
    async getDefaultPrompt() {
      return withLoading(() => callApi('prompt/default'));
    },
    // 获取用户 prompt
    async getUserPrompt() {
      return withLoading(() => callApi('prompt/user'));
    },
    // 保存用户 prompt
    async saveUserPrompt(prompt) {
      return withLoading(() => callApi('prompt/user', 'POST', { prompt }));
    }
  };

  return {
    ...api,
    loading,
    error
  };
} 