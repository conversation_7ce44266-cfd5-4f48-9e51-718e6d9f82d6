import { ref, onMounted, watch } from 'vue';

/**
 * 字幕管理逻辑的组合式函数
 * 处理字幕的初始化、合并和分拆等操作
 */
export function useSubtitleManager(props) {
  // 状态数据
  const subtitleItems = ref([]);
  const mergedGroups = ref([]);
  const originalRowsBackup = ref([]); // 保存原始数据以便分拆操作
  const rawSrtRows = ref([]); // 保存从SRT解析的、确保未合并状态的原始行数据，仅用于取消所有分组
  const hasInitialized = ref(false); // 标记是否已初始化

  // 监听抽屉显示变化
  watch(() => props.show, (newVal, oldVal) => {
    // 仅在抽屉打开时初始化
    if (newVal && !oldVal) {
      initializeItems();
    }
  });

  // 初始化时检查是否显示
  onMounted(() => {
    if (props.show) {
      initializeItems();
    }
  });

  /**
   * 初始化字幕项目
   * 处理传入的行数据，生成可显示的字幕项目
   */
  function initializeItems() {
    let sourceRows;
    
    // 从 props.rows 获取数据，保留合并状态
    if (props.rows && props.rows.length > 0) {
      sourceRows = JSON.parse(JSON.stringify(props.rows));
      
      // 如果是首次初始化，缓存未合并状态数据用于"取消所有分组"功能
      if (rawSrtRows.value.length === 0) {
        rawSrtRows.value = extractUnmergedRows(props.rows);
        console.log('rawSrtRows 已缓存用于取消所有分组功能');
      }
    } else {
      subtitleItems.value = [];
      mergedGroups.value = [];
      originalRowsBackup.value = [];
      return;
    }

    if (!sourceRows || sourceRows.length === 0) {
      subtitleItems.value = [];
      mergedGroups.value = [];
      originalRowsBackup.value = [];
      return;
    }

    // 记录合并项目的调试信息
    console.log('--- initializeItems Input (sourceRows) - Merged Items ---');
    if (sourceRows && sourceRows.length > 0) {
      sourceRows.forEach((row, i) => {
        if (row.isMerged) { 
          console.log(`Input Row ${i} (originalIndex: ${row.originalIndex}):`, JSON.parse(JSON.stringify({
            isMerged: row.isMerged,
            description: row.description?.substring(0, 50) + "...",
            mergedRowsCountFromInput: row.mergedRows ? row.mergedRows.length : 'N/A',
            mergedRowsSampleFromInput: row.mergedRows ? JSON.parse(JSON.stringify(row.mergedRows.slice(0,1))) : "N/A"
          })));
        }
      });
    }

    // 处理行数据
    processRows(sourceRows);
    hasInitialized.value = true;
  }

  /**
   * 从可能包含合并项的数据中提取所有未合并状态的行
   * 这确保了我们总是有一份干净的、未合并的原始数据
   * 注意：此函数仅用于"取消所有分组"功能
   */
  function extractUnmergedRows(rows) {
    const result = [];
    
    if (!rows || rows.length === 0) return result;
    
    // 对每一行进行处理
    rows.forEach((row, index) => {
      if (row.isMerged && row.mergedRows && row.mergedRows.length > 0) {
        // 如果是合并项，将其子项添加到结果中
        row.mergedRows.forEach((subRow, subIndex) => {
          result.push({
            ...subRow,
            description: subRow.description || '',
            originalIndex: subRow.originalIndex !== undefined ? subRow.originalIndex : (index * 100 + subIndex),
            index: subRow.index !== undefined ? subRow.index : subIndex + 1,
            startTime: subRow.startTime,
            endTime: subRow.endTime,
            duration: subRow.duration,
            isMerged: false,
            mergedRows: undefined,
            mergedRowsCount: undefined
          });
        });
      } else {
        // 如果不是合并项，直接添加到结果中
        result.push({
          ...row,
          description: row.description || '',
          originalIndex: row.originalIndex !== undefined ? row.originalIndex : index,
          index: row.index !== undefined ? row.index : index + 1,
          isMerged: false,
          mergedRows: undefined,
          mergedRowsCount: undefined
        });
      }
    });
    
    // 按startTime排序，确保顺序与原始SRT文件一致
    result.sort((a, b) => {
      if (a.startTime !== undefined && b.startTime !== undefined) {
        return a.startTime - b.startTime;
      }
      return a.originalIndex - b.originalIndex;
    });
    
    console.log(`提取了 ${result.length} 行未合并状态数据，用于取消分组功能`);
    return result;
  }

  /**
   * 处理行数据，转换为字幕项目
   */
  function processRows(sourceRows) {
    const safeRows = sourceRows.map((row, index) => {
      // 创建基础行拷贝
      const newRow = {
        ...row,
        originalIndex: row.originalIndex !== undefined ? row.originalIndex : index,
        index: row.index !== undefined ? row.index : index + 1,
        description: row.description || ''
      };

      // 从原始数据中恢复originalIndex
      if (originalRowsBackup.value && originalRowsBackup.value[index] && 
          originalRowsBackup.value[index].originalIndex !== undefined) {
        newRow.originalIndex = originalRowsBackup.value[index].originalIndex;
      }
      
      return newRow;
    });

    // 备份原始行数据
    try {
      originalRowsBackup.value = JSON.parse(JSON.stringify(safeRows.map(r => ({ ...r }))));
    } catch (e) {
      console.error('Error creating originalRowsBackup:', e);
      // 出错时创建简化备份
      originalRowsBackup.value = safeRows.map(row => ({
        originalIndex: row.originalIndex,
        description: row.description,
        isMerged: row.isMerged
      }));
    }

    // 创建字幕项目列表
    createSubtitleItems(safeRows);
  }

  /**
   * 创建字幕项目列表
   */
  function createSubtitleItems(safeRows) {
    try {
      subtitleItems.value = safeRows.map((row, index) => {
        const newSubtitleItem = {
          id: index + 1,
          content: row.description || '',
          isMerged: row.isMerged || false, // 保留合并状态
          originalIndex: row.originalIndex,
          rowIndex: row.index,
          originalRow: {},
          // 复制其他相关属性
          startTime: row.startTime,
          endTime: row.endTime,
          duration: row.duration,
          tags: Array.isArray(row.tags) ? [...row.tags] : (row.tags ? [row.tags] : []),
          keywords: row.keywords || '',
          imageSrc: row.imageSrc || '',
          imageAlt: row.imageAlt || '',
          isImageLocked: row.isImageLocked || false,
          thumbnails: Array.isArray(row.thumbnails) ? [...row.thumbnails] : []
        };

        if (row.isMerged && Array.isArray(row.mergedRows) && row.mergedRows.length > 0) {
          newSubtitleItem.isMerged = true;
          newSubtitleItem.originalRow.mergedRows = JSON.parse(JSON.stringify(row.mergedRows));

          if (newSubtitleItem.originalRow.mergedRows.length > 0) {
            // 如果有 originalState 属性，优先使用
            if (row.originalState) {
              newSubtitleItem.originalRow.originalState = JSON.parse(JSON.stringify(row.originalState));
            } else {
              // 否则使用第一个子项作为原始状态
              const firstOriginalItemData = newSubtitleItem.originalRow.mergedRows[0];
              newSubtitleItem.originalRow.originalState = JSON.parse(JSON.stringify(firstOriginalItemData));
            }
          } else {
            console.warn("[AdjustShotsDrawer InitializeItems] Merged item has no mergedRows, falling back for originalState", JSON.parse(JSON.stringify(row)));
            const fallbackState = JSON.parse(JSON.stringify(row));
            delete fallbackState.mergedRows;
            delete fallbackState.mergedRowsCount;
            delete fallbackState.originalState;
            newSubtitleItem.originalRow.originalState = fallbackState;
          }
        } else {
          newSubtitleItem.isMerged = false;
          const pristineCopyOfRow = JSON.parse(JSON.stringify(row));
          delete pristineCopyOfRow.mergedRows;
          delete pristineCopyOfRow.mergedRowsCount;
          delete pristineCopyOfRow.originalState;
          newSubtitleItem.originalRow = pristineCopyOfRow;
        }
        return newSubtitleItem;
      });

      // 调试信息
      console.log('--- initializeItems Output (subtitleItems) - Merged Items ---');
      if (subtitleItems.value && subtitleItems.value.length > 0) {
        subtitleItems.value.forEach((item, i) => {
          if (item.isMerged) { 
            console.log(`SubtitleItem ${i} (originalIndex: ${item.originalIndex}):`, JSON.parse(JSON.stringify({
              isMerged: item.isMerged,
              content: item.content?.substring(0, 50) + "...",
              originalRow_mergedRowsCount: item.originalRow && item.originalRow.mergedRows ? item.originalRow.mergedRows.length : 'N/A',
              originalRow_originalStateExists: !!(item.originalRow && item.originalRow.originalState)
            })));
          }
        });
      }

      // 收集合并组信息
      collectMergedGroups();
      
      // 重新编号所有项
      renumberItems();
      
      console.log('[AdjustShotsDrawer InitializeItems] Completed. subtitleItems count:', subtitleItems.value.length);
    } catch (error) {
      console.error('Error initializing subtitleItems or mergedGroups:', error);
      subtitleItems.value = [];
      mergedGroups.value = [];
    }
  }

  /**
   * 收集合并组信息并更新备份
   */
  function collectMergedGroups() {
    mergedGroups.value = [];
    subtitleItems.value.forEach(item => {
      if (item.isMerged && item.originalRow && Array.isArray(item.originalRow.mergedRows) && item.originalRow.mergedRows.length > 0) {
        const group = [item.originalIndex].concat(item.originalRow.mergedRows.map(subRow => subRow.originalIndex));
        mergedGroups.value.push(group);
      }
    });

    // 更新原始数据备份以支持拆分
    originalRowsBackup.value = subtitleItems.value.map(item => {
      const row = { ...item.originalRow };
      row.originalIndex = item.originalIndex;
      return row;
    });

    // 添加额外的子行到备份中
    let additionalRows = [];
    originalRowsBackup.value.forEach((row) => {
      if (row.isMerged && Array.isArray(row.mergedRows) && row.mergedRows.length > 0) {
        row.mergedRows.forEach(subRow => {
          if (!originalRowsBackup.value.some(r => r.originalIndex === subRow.originalIndex)) {
            additionalRows.push({ ...subRow, isMerged: false });
          }
        });
      }
    });

    if (additionalRows.length > 0) {
      originalRowsBackup.value = [...originalRowsBackup.value, ...additionalRows];
    }
  }

  /**
   * 取消所有合并分组，恢复到初始状态
   */
  function cancelAllMergedGroups() {
    console.log('--- cancelAllMergedGroups START ---');
    if (rawSrtRows.value && rawSrtRows.value.length > 0) {
      // 使用缓存的未合并状态的原始行重新初始化
      const pristineRowsCopy = JSON.parse(JSON.stringify(rawSrtRows.value));
      
      // 按照startTime排序，确保与原始SRT文件顺序一致
      pristineRowsCopy.sort((a, b) => {
        // 优先使用startTime进行排序
        if (a.startTime !== undefined && b.startTime !== undefined) {
          return a.startTime - b.startTime;
        }
        // 如果没有startTime，则使用originalIndex或index
        if (a.originalIndex !== undefined && b.originalIndex !== undefined) {
          return a.originalIndex - b.originalIndex;
        }
        return (a.index || 0) - (b.index || 0);
      });
      
      // 重新构建字幕项
      subtitleItems.value = pristineRowsCopy.map((row, index) => ({
        id: index + 1,
        content: row.description || '',
        isMerged: false,
        originalIndex: row.originalIndex,
        rowIndex: row.index,
        startTime: row.startTime,
        endTime: row.endTime,
        duration: row.duration,
        originalRow: JSON.parse(JSON.stringify(row))
      }));
      
      mergedGroups.value = [];
      renumberItems();
      console.log('Items reset from rawSrtRows and sorted by startTime. Count:', subtitleItems.value.length);
    } else {
      console.warn('rawSrtRows is empty, falling back to re-initializing with current props.rows');
      // 如果rawSrtRows为空，尝试从当前props.rows提取未合并数据
      rawSrtRows.value = extractUnmergedRows(props.rows);
      cancelAllMergedGroups(); // 递归调用自身，现在应该有rawSrtRows数据了
    }
    console.log('--- cancelAllMergedGroups END ---');
  }

  /**
   * 处理左键点击 - 向上合并
   */
  function handleClick(index) {
    if (index > 0) {
      mergeWithPrevious(index);
    } else {
      console.log('第一行无法向上合并');
    }
  }

  /**
   * 处理右键点击 - 分拆合并项
   */
  function handleRightClick(index) {
    console.log(`--- handleRightClick START (index: ${index}) ---`);
    const item = subtitleItems.value[index];
    
    if (!item) {
      console.error('无法找到索引为', index, '的项目');
      return;
    }
    
    // 检查是否为合并项
    if (item.isMerged) {
      // 检查是否具有必要的数据结构
      if (!item.originalRow) {
        console.error('合并项缺少originalRow数据');
        item.originalRow = { mergedRows: [] };
      }
      
      // 确保mergedRows存在
      if (!Array.isArray(item.originalRow.mergedRows) || item.originalRow.mergedRows.length === 0) {
        console.error('合并项没有子行数据');
        return;
      }
      
      // 检查originalState是否存在，如果不存在则创建
      if (!item.originalRow.originalState) {
        console.log('合并项缺少originalState, 尝试创建');
        if (item.originalRow.mergedRows.length > 0) {
          const firstRow = { ...item.originalRow.mergedRows[0] };
          item.originalRow.originalState = {
            originalIndex: firstRow.originalIndex,
            index: firstRow.index || 1,
            description: firstRow.description || '',
            startTime: firstRow.startTime,
            endTime: firstRow.endTime,
            duration: firstRow.duration,
            isMerged: false,
            mergedWith: []
          };
          console.log('已为合并项创建originalState:', item.originalRow.originalState);
        } else {
          console.error('无法创建originalState，因为没有子行数据');
          return;
        }
      }
      
      // 执行分拆操作
      splitMergedItem(index);
    } else {
      console.log('此项未合并，无法分拆');
    }
    console.log('--- handleRightClick END ---');
  }

  /**
   * 向上合并逻辑 - 将当前项与上一项合并
   */
  function mergeWithPrevious(index) {
    console.log(`--- mergeWithPrevious START (index: ${index}) ---`);
    if (index <= 0) {
      console.log('无法合并索引 <= 0');
      return;
    }

    const prevItemIndex = index - 1;
    const currentItem = subtitleItems.value[index];
    const prevItem = subtitleItems.value[prevItemIndex];

    if (!prevItem || !currentItem) {
      console.error('mergeWithPrevious: prevItem or currentItem not found.');
      return;
    }

    // 1. 获取或创建 prevItem.originalRow，确保其结构完整
    if (!prevItem.originalRow || typeof prevItem.originalRow !== 'object') {
      prevItem.originalRow = JSON.parse(JSON.stringify({
        description: prevItem.content,
        isMerged: false,
        originalIndex: prevItem.originalIndex,
        index: prevItem.rowIndex,
        startTime: prevItem.startTime,
        endTime: prevItem.endTime,
        duration: prevItem.duration,
        tags: Array.isArray(prevItem.tags) ? [...prevItem.tags] : [],
        keywords: prevItem.keywords || '',
        imageSrc: prevItem.imageSrc || '',
        mergedRows: []
      }));
    }

    // 2. 设置 originalState (如果 prevItem 正在从非合并项转为合并项)
    if (!prevItem.isMerged) { 
      prevItem.originalRow.originalState = JSON.parse(JSON.stringify(prevItem.originalRow));
      delete prevItem.originalRow.originalState.mergedRows;
      prevItem.originalRow.originalState.isMerged = false; 
      prevItem.originalRow.mergedRows = [JSON.parse(JSON.stringify(prevItem.originalRow.originalState))];
    } else {
      if (!prevItem.originalRow.originalState) {
        console.warn("mergeWithPrevious: prevItem isMerged but no originalState. Reconstructing.");
        if (prevItem.originalRow.mergedRows && prevItem.originalRow.mergedRows.length > 0) {
          prevItem.originalRow.originalState = JSON.parse(JSON.stringify(prevItem.originalRow.mergedRows[0]));
        } else {
          const tempState = JSON.parse(JSON.stringify(prevItem.originalRow));
          delete tempState.mergedRows; 
          delete tempState.originalState; 
          tempState.isMerged = false;
          prevItem.originalRow.originalState = tempState;
          prevItem.originalRow.mergedRows = [JSON.parse(JSON.stringify(tempState))];
        }
      }
      if (!Array.isArray(prevItem.originalRow.mergedRows)) {
        prevItem.originalRow.mergedRows = [];
        if(prevItem.originalRow.originalState && prevItem.originalRow.mergedRows.length === 0){
          prevItem.originalRow.mergedRows.push(JSON.parse(JSON.stringify(prevItem.originalRow.originalState)));
        }
      }
    }

    // 3. 准备待合并的 currentItem 的数据
    const itemsToAddFromCurrent = [];
    if (currentItem.isMerged && currentItem.originalRow && Array.isArray(currentItem.originalRow.mergedRows) && currentItem.originalRow.mergedRows.length > 0) {
      itemsToAddFromCurrent.push(...JSON.parse(JSON.stringify(currentItem.originalRow.mergedRows)));
    } else {
      let currentOriginalSource = currentItem.originalRow;
      if (!currentOriginalSource || typeof currentOriginalSource !== 'object' || currentOriginalSource.description === undefined) {
          currentOriginalSource = currentItem; // Fallback to currentItem itself if originalRow is not proper
      }
      const currentOriginal = JSON.parse(JSON.stringify({
          description: currentOriginalSource.content || currentOriginalSource.description || '',
          isMerged: false,
          originalIndex: currentOriginalSource.originalIndex,
          index: currentOriginalSource.rowIndex || currentOriginalSource.index,
          startTime: currentOriginalSource.startTime,
          endTime: currentOriginalSource.endTime,
          duration: currentOriginalSource.duration,
          tags: Array.isArray(currentOriginalSource.tags) ? [...currentOriginalSource.tags] : [],
          keywords: currentOriginalSource.keywords || '',
          imageSrc: currentOriginalSource.imageSrc || '',
        }));
      itemsToAddFromCurrent.push(currentOriginal);
    }

    // 4. 将 itemsToAddFromCurrent 添加到 prevItem.originalRow.mergedRows (去重)
    itemsToAddFromCurrent.forEach(itemToAdd => {
      const exists = prevItem.originalRow.mergedRows.some(
        existing => existing.originalIndex === itemToAdd.originalIndex
      );
      if (!exists) {
        prevItem.originalRow.mergedRows.push(itemToAdd);
      }
    });

    // 5. 排序 mergedRows (基于 originalIndex)
    prevItem.originalRow.mergedRows.sort((a, b) => a.originalIndex - b.originalIndex);

    // 6. 更新 prevItem 的显示内容和状态
    console.log('mergedRows内容:', prevItem.originalRow.mergedRows.map(r => r.description));
    prevItem.content = prevItem.originalRow.mergedRows
      .map(r => r.description || r.content || '[空]')
      .join('\n');
    prevItem.isMerged = true;
    prevItem.originalRow.isMerged = true;
    prevItem.originalRow.description = prevItem.content;

    // 7. 更新 prevItem 的聚合属性 (如时间范围等)
    updateMergedItemProperties(prevItem);
    
    // 8. 更新合并组信息
    updateMergedGroups(prevItem, itemsToAddFromCurrent);

    // 9. 从显示列表中移除当前项
    subtitleItems.value = subtitleItems.value.filter((_, i) => i !== index);

    // 10. 重新编号项目
    renumberItems();
    console.log(`mergeWithPrevious: Merged item at index ${prevItemIndex}. New content: "${prevItem.content.substring(0,50)}...". mergedRows count: ${prevItem.originalRow.mergedRows.length}`);
    console.log('--- mergeWithPrevious END ---');
  }

  /**
   * 更新合并项的属性（如时间范围）
   */
  function updateMergedItemProperties(item) {
    if (item.originalRow.mergedRows.length > 0) {
      const startTimes = item.originalRow.mergedRows
        .map(r => r.startTime)
        .filter(t => t !== undefined && !isNaN(parseFloat(t)));
      const endTimes = item.originalRow.mergedRows
        .map(r => r.endTime)
        .filter(t => t !== undefined && !isNaN(parseFloat(t)));
        
      if (startTimes.length > 0) item.startTime = Math.min(...startTimes);
      if (endTimes.length > 0) item.endTime = Math.max(...endTimes);
      
      if (item.startTime !== undefined && item.endTime !== undefined && 
          !isNaN(parseFloat(item.startTime)) && !isNaN(parseFloat(item.endTime))) {
        item.duration = item.endTime - item.startTime;
      }
      
      item.originalRow.startTime = item.startTime;
      item.originalRow.endTime = item.endTime;
      item.originalRow.duration = item.duration;
    }
  }

  /**
   * 更新合并组信息
   */
  function updateMergedGroups(prevItem, itemsToAddFromCurrent) {
    const existingGroupIndex = mergedGroups.value.findIndex(group => 
      group.includes(prevItem.originalIndex)
    );
    const newGroupOriginalIndices = prevItem.originalRow.mergedRows.map(r => r.originalIndex);
    
    if (existingGroupIndex > -1) {
      mergedGroups.value.splice(existingGroupIndex, 1, newGroupOriginalIndices);
    } else {
      mergedGroups.value.push(newGroupOriginalIndices);
    }
    
    // 如果 currentItem 之前属于某个合并组，处理旧的合并组
    const currentItemOriginalIndices = itemsToAddFromCurrent.map(i => i.originalIndex);
    mergedGroups.value = mergedGroups.value.filter(group => 
      !currentItemOriginalIndices.some(ciOriginalIndex => 
        group.includes(ciOriginalIndex) && ciOriginalIndex !== prevItem.originalIndex
      )
    );
    
    // 确保最终的 prevItem 所在的组是唯一的
    mergedGroups.value = mergedGroups.value.filter((group, idx, self) => 
      idx === self.findIndex(g => 
        g.every((val, i) => val === group[i]) && g.length === group.length
      )
    );
  }

  /**
   * 分拆已合并的项
   */
  function splitMergedItem(index) {
    console.log(`--- splitMergedItem START (index: ${index}) ---`);
    const itemToSplit = subtitleItems.value[index];

    if (!itemToSplit || !itemToSplit.isMerged) {
      console.log('此项未合并或无效，无法分拆');
      return;
    }

    const originalData = itemToSplit.originalRow;

    // 验证数据完整性
    if (!originalData) {
      console.error('splitMergedItem: 分拆失败 - 无效的 originalData');
      return;
    }

    // 检查 mergedRows 是否存在且有效
    if (!Array.isArray(originalData.mergedRows) || originalData.mergedRows.length === 0) {
      console.error('splitMergedItem: 分拆失败 - 无效的 mergedRows。', JSON.parse(JSON.stringify(originalData)));
      alert('分拆失败：项目没有合并的子行数据。');
      return;
    }

    // 检查 originalState 是否存在，如果不存在则从第一个子行创建
    if (!originalData.originalState || typeof originalData.originalState !== 'object') {
      console.log('splitMergedItem: originalState 不存在，尝试从第一个子行创建');
      originalData.originalState = { ...originalData.mergedRows[0], isMerged: false };
    }

    // 准备恢复项目
    const restoredItems = [];

    // 恢复容器行自身 (基于 originalState)
    const containerRestored = createRestoredItem(originalData.originalState);
    restoredItems.push(containerRestored);

    // 恢复其他被合并的子项
    originalData.mergedRows.forEach(subRowData => {
      if (subRowData.originalIndex === originalData.originalState.originalIndex) {
        return; // 跳过容器行自身
      }
      const restoredSubItem = createRestoredItem(subRowData);
      restoredItems.push(restoredSubItem);
    });

    // 按原始索引排序
    restoredItems.sort((a, b) => a.originalIndex - b.originalIndex);

    // 从合并组中移除相关组
    mergedGroups.value = mergedGroups.value.filter(group => 
      !group.includes(itemToSplit.originalIndex)
    );
    
    // 替换原合并项为恢复的项目
    subtitleItems.value.splice(index, 1, ...restoredItems);
    
    // 重新编号
    renumberItems();
    
    console.log(`成功分拆项 ${index} 为 ${restoredItems.length} 个原始项.`);
    console.log('--- splitMergedItem END ---');
  }

  /**
   * 创建恢复项
   */
  function createRestoredItem(rowData) {
    const restoredItem = {
      id: 0, // 将在renumberItems中更新
      content: rowData.description,
      isMerged: false,
      originalIndex: rowData.originalIndex,
      rowIndex: rowData.index,
      originalRow: JSON.parse(JSON.stringify(rowData)),
      startTime: rowData.startTime,
      endTime: rowData.endTime,
      duration: rowData.duration,
      tags: Array.isArray(rowData.tags) ? [...rowData.tags] : [],
      keywords: rowData.keywords || '',
      imageSrc: rowData.imageSrc || '',
    };
    
    // 移除可能导致循环引用的属性
    delete restoredItem.originalRow.mergedRows;
    delete restoredItem.originalRow.originalState;
    restoredItem.originalRow.isMerged = false;
    
    return restoredItem;
  }

  /**
   * 重新为所有项编号
   */
  function renumberItems() {
    subtitleItems.value.forEach((item, index) => {
      item.id = index + 1;
    });
  }

  /**
   * 确认合并逻辑，收集结果并发送给父组件
   */
  function confirmMerge(emit) {
    console.log('--- confirmMerge START ---');
    console.log('准备确认合并，当前 subtitleItems 数量:', subtitleItems.value.length);

    // 处理数据结构，使其与 useStudioActions.js 的 mergeUp 和 splitDown 函数兼容
    const finalRowsState = prepareFinalRowsState();

    // 检查是否有合并行
    const hasMergedRows = finalRowsState.some(row => 
      row.isMerged && row.mergedRows && row.mergedRows.length > 0
    );
    
    if (hasMergedRows) {
      console.log('结果包含合并行，设置合并状态标记');
    } else {
      console.log('结果不包含合并行');
    }

    // 发送事件更新父组件中的行数据
    emit('update-rows', finalRowsState);
    
    // 关闭抽屉
    emit('update:show', false);
    
    // 新增：同步 rawSrtRows，保证取消分组时是最新的未合并状态
    rawSrtRows.value = extractUnmergedRows(finalRowsState);
    
    // 清除内存，避免内存泄漏
    mergedGroups.value = [];
    subtitleItems.value = [];
    originalRowsBackup.value = [];
    // 注意：不清空rawSrtRows，保留用于下次取消分组功能
    
    console.log('已发送 update-rows 和 update:show 事件');
  }

  /**
   * 准备最终的行状态数据
   */
  function prepareFinalRowsState() {
    const finalRowsState = [];
    
    // 遍历当前显示的字幕项目，生成输出状态
    for (let i = 0; i < subtitleItems.value.length; i++) {
      const item = subtitleItems.value[i];
      
      // 构建基本信息
      const rowState = {
        index: i + 1, // 确保连续的索引，从1开始
        description: item.content,
        isMerged: item.isMerged,
        originalIndex: item.originalIndex
      };
      
      // 复制其他关键属性
      if (item.originalRow) {
        copyPropertiesFromOriginalRow(item, rowState);
      }
      
      // 处理合并行数据
      if (item.isMerged && item.originalRow && 
          Array.isArray(item.originalRow.mergedRows) && 
          item.originalRow.mergedRows.length > 0) {
        
        processMergedRowState(item, rowState);
      } else {
        // 非合并行处理
        rowState.mergedRows = [];
        rowState.isMerged = false;
      }
      
      finalRowsState.push(rowState);
    }

    // 调试输出
    logMergedItemsDebugInfo(finalRowsState);

    return finalRowsState;
  }

  /**
   * 从原始行复制属性到目标行状态对象
   */
  function copyPropertiesFromOriginalRow(item, rowState) {
    // 复制时间信息
    rowState.startTime = item.originalRow.startTime;
    rowState.endTime = item.originalRow.endTime;
    rowState.duration = item.originalRow.duration;
    
    // 复制标签和关键词
    rowState.tags = Array.isArray(item.originalRow.tags) ? [...item.originalRow.tags] : [];
    rowState.keywords = item.originalRow.keywords || '';
    
    // 复制图片相关信息
    rowState.imageSrc = item.originalRow.imageSrc || '';
    rowState.imageAlt = item.originalRow.imageAlt || '';
    rowState.isImageLocked = item.originalRow.isImageLocked || false;
    rowState.thumbnails = Array.isArray(item.originalRow.thumbnails) ? 
      [...item.originalRow.thumbnails] : [];
  }

  /**
   * 处理合并行的状态
   */
  function processMergedRowState(item, rowState) {
    console.log(`处理第 ${rowState.index} 行的合并数据，合并子项数量:`, 
      item.originalRow.mergedRows.length);
    
    // 添加 mergedRows 数组，确保格式兼容
    rowState.mergedRows = item.originalRow.mergedRows.map(subRow => {
      // 创建干净的复制，避免循环引用
      return {
        originalIndex: subRow.originalIndex,
        index: subRow.index,
        description: subRow.description || '',
        startTime: subRow.startTime,
        endTime: subRow.endTime,
        duration: subRow.duration,
        tags: Array.isArray(subRow.tags) ? [...subRow.tags] : [],
        keywords: subRow.keywords || '',
        isMerged: false // 子行应该都是非合并状态
      };
    });
    
    // 确保合并行数据有 mergedRowsCount 字段
    rowState.mergedRowsCount = rowState.mergedRows.length;
    
    // 更新时间范围（如果需要）
    calculateTimeRangeForMergedRows(rowState);
  }

  /**
   * 计算合并行的时间范围
   */
  function calculateTimeRangeForMergedRows(rowState) {
    if (rowState.mergedRows.length > 0) {
      const startTimes = rowState.mergedRows
        .map(row => row.startTime)
        .filter(time => time !== undefined && !isNaN(parseFloat(time)));
        
      const endTimes = rowState.mergedRows
        .map(row => row.endTime)
        .filter(time => time !== undefined && !isNaN(parseFloat(time)));
        
      if (startTimes.length > 0) {
        rowState.startTime = Math.min(...startTimes);
      }
      
      if (endTimes.length > 0) {
        rowState.endTime = Math.max(...endTimes);
      }
      
      if (rowState.startTime !== undefined && rowState.endTime !== undefined) {
        rowState.duration = rowState.endTime - rowState.startTime;
      }
    }
  }

  /**
   * 记录合并项的调试信息
   */
  function logMergedItemsDebugInfo(finalRowsState) {
    console.log('--- confirmMerge Output for Merged Items ---');
    finalRowsState.forEach((rowState, i) => {
      if (rowState.isMerged) {
        console.log(`Output Row ${i + 1} (originalIndex: ${rowState.originalIndex}):`, 
          JSON.parse(JSON.stringify({
            isMerged: rowState.isMerged,
            description: rowState.description?.substring(0, 50) + "...",
            mergedRowsCount: rowState.mergedRows ? rowState.mergedRows.length : 0,
            mergedRowsSample: rowState.mergedRows ? 
              JSON.parse(JSON.stringify(rowState.mergedRows.slice(0,1))) : "N/A"
          }))
        );
      }
    });
  }

  /**
   * 导出文本
   */
  async function exportText() {
    if (subtitleItems.value.length === 0) {
      alert('当前没有文本可以导出');
      console.log('没有文本可以导出');
      return;
    }
    
    console.log(`准备导出 ${subtitleItems.value.length} 条文本`);
    
    // 收集所有文本并格式化
    const formattedItems = collectFormattedItems();
    const formattedText = formattedItems.join('\n');
    const clipboardText = formattedText;
    
    try {
      // 复制到剪贴板
      await navigator.clipboard.writeText(clipboardText);
      console.log('已复制所有文本到剪贴板');
      
      // 尝试获取项目和章节信息
      const { projectTitle, chapterTitle } = getProjectAndChapterInfo();
      
      // 如果有项目和章节信息，尝试保存到文件
      if (projectTitle && chapterTitle) {
        await saveTextToFile(formattedText, projectTitle, chapterTitle, formattedItems.length);
      } else {
        alert(`已导出 ${formattedItems.length} 条文本！\n\n内容已复制到剪贴板`);
      }
    } catch (error) {
      console.error('导出文本过程出错:', error);
      alert(`导出文本时出错: ${error.message}`);
    }
    
    console.log('--- exportText END ---');
  }

  /**
   * 收集格式化的文本项目
   */
  function collectFormattedItems() {
    const items = [];
    subtitleItems.value.forEach((item, index) => {
      if (item.content) {
        // 处理合并句子，将换行符替换为逗号
        const formattedContent = item.content.replace(/\n/g, ',');
        items.push(`${index + 1}. ${formattedContent}`);
      }
    });
    return items;
  }

  /**
   * 获取项目名和章节名
   */
  function getProjectAndChapterInfo() {
    try {
      // 尝试从URL获取项目和章节信息
      const urlParams = new URLSearchParams(window.location.search);
      let projectTitle = urlParams.get('project') || '';
      let chapterTitle = urlParams.get('chapter') || '';
      
      if (!projectTitle || !chapterTitle) {
        // 如果URL中没有，尝试从localStorage获取
        const currentProject = localStorage.getItem('currentProject');
        const currentChapter = localStorage.getItem('currentChapter');
        
        if (currentProject) projectTitle = currentProject;
        if (currentChapter) chapterTitle = currentChapter;
      }
      
      return { projectTitle, chapterTitle };
    } catch (error) {
      console.error('获取项目和章节信息失败:', error);
      return { projectTitle: '', chapterTitle: '' };
    }
  }

  /**
   * 保存文本到文件
   */
  async function saveTextToFile(text, projectTitle, chapterTitle, itemCount) {
    try {
      // 构建保存路径
      const savePath = `draft/${projectTitle}/${chapterTitle}/exported-text.txt`;
      console.log('准备保存文本到文件:', savePath);
      
      // 调用API保存文件
      const response = await fetch('/api/local/save-text-file', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ filePath: savePath, content: text })
      });
      
      const result = await response.json();
      
      if (result.success) {
        alert(`已成功导出 ${itemCount} 条文本！\n\n文件已保存到: ${savePath}\n同时已复制到剪贴板`);
      } else {
        console.error('保存文件失败:', result.error);
        alert(`已导出 ${itemCount} 条文本！\n\n保存文件失败: ${result.error}\n但内容已复制到剪贴板`);
      }
    } catch (error) {
      console.error('保存文件过程中出错:', error);
      alert(`已导出 ${itemCount} 条文本！\n\n保存文件失败，但内容已复制到剪贴板`);
    }
  }

  /**
   * 导入分组文本
   */
  function importGroups() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.txt';
    input.onchange = handleFileSelection;
    input.click();
  }

  /**
   * 处理文件选择
   */
  function handleFileSelection(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = (e) => parseImportedContent(e.target.result);
    reader.readAsText(file);
  }

  /**
   * 解析导入的内容
   */
  function parseImportedContent(content) {
    try {
      // 使用正则表达式匹配格式为 "1. xxx, xxx, xxx" 的行
      const groupRegex = /(\d+)\.\s*(.+)/g;
      const parsedGroups = [];
      let match;
      
      while ((match = groupRegex.exec(content)) !== null) {
        const groupNumber = parseInt(match[1]);
        const groupContent = match[2].trim();
        
        // 按逗号分隔内容，得到各个子句
        const sentences = groupContent.split(',')
          .map(s => s.trim())
          .filter(s => s.length > 0);
        
        if (sentences.length > 0) {
          parsedGroups.push({
            number: groupNumber,
            content: sentences.join('\n'),
            sentences: sentences,
            cleanContent: getCleanContent(sentences.join(''))
          });
        }
      }
      
      if (parsedGroups.length === 0) {
        alert('导入的文件格式不正确，请使用"1. xxx, xxx, xxx"的格式');
        return;
      }
      
      console.log(`成功解析 ${parsedGroups.length} 个分组`);
      convertParsedGroupsToSubtitleItems(parsedGroups);
    } catch (error) {
      console.error('导入分组时出错:', error);
      alert(`导入分组时出错: ${error.message}`);
    }
  }

  /**
   * 将解析的分组转换为字幕项目
   */
  function convertParsedGroupsToSubtitleItems(parsedGroups) {
    // 首先保存原始数据的映射关系，以便后续查找匹配
    const originalDataMap = createOriginalDataMap();
    
    // 将解析出的分组转换为subtitleItems
    subtitleItems.value = parsedGroups.map((group, index) => {
      // 创建合并项
      const isMerged = group.sentences.length > 1;
      
      // 根据内容查找匹配的原始数据行
      const matchingOriginalRows = findMatchingOriginalRows(group, originalDataMap);
      
      const subtitleItem = {
        id: index + 1,
        content: group.content,
        isMerged: isMerged,
        originalIndex: index
      };
      
      // 复制匹配行中的第一个行的时间信息（如果有）
      if (matchingOriginalRows.length > 0) {
        const firstMatch = matchingOriginalRows[0];
        subtitleItem.startTime = firstMatch.startTime;
        subtitleItem.endTime = firstMatch.endTime;
        subtitleItem.duration = firstMatch.duration;
        subtitleItem.originalIndex = firstMatch.originalIndex;
      }
      
      // 设置原始行信息
      if (isMerged) {
        subtitleItem.originalRow = {
          description: group.content,
          isMerged: true,
          mergedRows: []
        };
        
        // 为每个句子创建子行，尝试匹配原始数据
        group.sentences.forEach((sentence, sentIndex) => {
          const matchingSentenceRow = findSentenceMatch(sentence, matchingOriginalRows, originalDataMap);
          
          const mergedRow = {
            originalIndex: matchingSentenceRow?.originalIndex || (index * 100 + sentIndex),
            index: sentIndex + 1,
            description: sentence,
            isMerged: false
          };
          
          // 复制匹配行的时间信息（如果有）
          if (matchingSentenceRow) {
            mergedRow.startTime = matchingSentenceRow.startTime;
            mergedRow.endTime = matchingSentenceRow.endTime;
            mergedRow.duration = matchingSentenceRow.duration;
          }
          
          subtitleItem.originalRow.mergedRows.push(mergedRow);
        });
        
        // 设置时间范围（取最小的startTime和最大的endTime）
        updateMergedItemTimeRange(subtitleItem);
        
        // 重要：设置originalState，确保可以立即分拆
        // 复制第一个子项作为原始状态
        if (subtitleItem.originalRow.mergedRows.length > 0) {
          const firstRow = { ...subtitleItem.originalRow.mergedRows[0] };
          
          // 确保originalState具有正确的属性
          subtitleItem.originalRow.originalState = {
            originalIndex: firstRow.originalIndex,
            index: firstRow.index,
            description: firstRow.description,
            startTime: firstRow.startTime,
            endTime: firstRow.endTime,
            duration: firstRow.duration,
            isMerged: false,
            mergedWith: []
          };
        }
      } else {
        subtitleItem.originalRow = {
          description: group.content,
          isMerged: false
        };
        
        // 复制匹配行的属性
        if (matchingOriginalRows.length > 0) {
          const match = matchingOriginalRows[0];
          Object.assign(subtitleItem.originalRow, {
            startTime: match.startTime,
            endTime: match.endTime,
            duration: match.duration,
            originalIndex: match.originalIndex
          });
        }
      }
      
      return subtitleItem;
    });
    
    // 更新合并组信息和备份
    updateBackupAndGroups();
    
    // 尝试按时间顺序排序
    sortSubtitleItemsByTime();
    
    // 显示导入结果
    alert(`已导入 ${parsedGroups.length} 个分组，其中包含合并项 ${mergedGroups.value.length} 个`);
  }

  /**
   * 创建原始数据的映射关系，用于快速查找匹配
   */
  function createOriginalDataMap() {
    const map = {
      byContent: new Map(),
      byCleanContent: new Map(),
      allRows: []
    };
    
    // 如果有原始数据，创建映射
    if (props.rows && props.rows.length > 0) {
      props.rows.forEach(row => {
        // 存储原始行
        map.allRows.push(row);
        
        // 按内容创建映射
        const content = row.description || '';
        map.byContent.set(content, row);
        
        // 按清理后的内容创建映射
        const cleanContent = getCleanContent(content);
        if (cleanContent) {
          if (!map.byCleanContent.has(cleanContent)) {
            map.byCleanContent.set(cleanContent, []);
          }
          map.byCleanContent.get(cleanContent).push(row);
        }
        
        // 如果是合并项，也对子项创建映射
        if (row.isMerged && row.mergedRows && row.mergedRows.length > 0) {
          row.mergedRows.forEach(subRow => {
            const subContent = subRow.description || '';
            map.byContent.set(subContent, subRow);
            
            const subCleanContent = getCleanContent(subContent);
            if (subCleanContent) {
              if (!map.byCleanContent.has(subCleanContent)) {
                map.byCleanContent.set(subCleanContent, []);
              }
              map.byCleanContent.get(subCleanContent).push(subRow);
            }
          });
        }
      });
    }
    
    return map;
  }

  /**
   * 查找与分组内容匹配的原始行
   */
  function findMatchingOriginalRows(group, originalDataMap) {
    const matching = [];
    
    // 尝试精确匹配完整内容
    if (originalDataMap.byContent.has(group.content)) {
      matching.push(originalDataMap.byContent.get(group.content));
    }
    
    // 然后尝试匹配每个单独的句子
    group.sentences.forEach(sentence => {
      if (originalDataMap.byContent.has(sentence)) {
        matching.push(originalDataMap.byContent.get(sentence));
      }
    });
    
    // 如果没有精确匹配，尝试使用清理后的内容匹配
    if (matching.length === 0) {
      const cleanContent = group.cleanContent;
      if (originalDataMap.byCleanContent.has(cleanContent)) {
        matching.push(...originalDataMap.byCleanContent.get(cleanContent));
      }
    }
    
    // 去重
    return [...new Set(matching)];
  }

  /**
   * 查找与句子匹配的行
   */
  function findSentenceMatch(sentence, matchingOriginalRows, originalDataMap) {
    // 首先检查是否有精确匹配
    if (originalDataMap.byContent.has(sentence)) {
      return originalDataMap.byContent.get(sentence);
    }
    
    // 然后检查清理后的内容是否匹配
    const cleanSentence = getCleanContent(sentence);
    if (cleanSentence && originalDataMap.byCleanContent.has(cleanSentence)) {
      const matches = originalDataMap.byCleanContent.get(cleanSentence);
      if (matches && matches.length > 0) {
        return matches[0];
      }
    }
    
    // 如果没有找到匹配，返回null
    return null;
  }

  /**
   * 更新合并项的时间范围
   */
  function updateMergedItemTimeRange(item) {
    if (item.originalRow && item.originalRow.mergedRows && item.originalRow.mergedRows.length > 0) {
      const startTimes = item.originalRow.mergedRows
        .map(row => row.startTime)
        .filter(time => time !== undefined && !isNaN(parseFloat(time)));
        
      const endTimes = item.originalRow.mergedRows
        .map(row => row.endTime)
        .filter(time => time !== undefined && !isNaN(parseFloat(time)));
        
      if (startTimes.length > 0) {
        item.startTime = Math.min(...startTimes);
        item.originalRow.startTime = item.startTime;
      }
      
      if (endTimes.length > 0) {
        item.endTime = Math.max(...endTimes);
        item.originalRow.endTime = item.endTime;
      }
      
      if (item.startTime !== undefined && item.endTime !== undefined) {
        item.duration = item.endTime - item.startTime;
        item.originalRow.duration = item.duration;
      }
    }
  }

  /**
   * 按时间顺序排序字幕项
   */
  function sortSubtitleItemsByTime() {
    // 尝试按startTime排序
    const hasTimeInfo = subtitleItems.value.some(item => item.startTime !== undefined);
    
    if (hasTimeInfo) {
      subtitleItems.value.sort((a, b) => {
        if (a.startTime !== undefined && b.startTime !== undefined) {
          return a.startTime - b.startTime;
        }
        return a.id - b.id;
      });
      
      // 重新编号
      renumberItems();
    }
  }

  /**
   * 获取清理后的内容（用于字数比较）
   */
  function getCleanContent(text) {
    if (!text) return '';
    // 移除所有标点符号、空白字符等，只保留汉字、字母和数字
    return text.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '');
  }

  /**
   * 计算文本中的字数（不含标点）
   */
  function countWords(text) {
    if (!text) return 0;
    // 先获取清理后的内容，再计算长度
    return getCleanContent(text).length;
  }

  /**
   * 更新备份和合并组
   */
  function updateBackupAndGroups() {
    // 清空或重建合并组
    mergedGroups.value = [];
    
    // 收集合并组信息
    subtitleItems.value.forEach(item => {
      if (item.isMerged && item.originalRow && 
          Array.isArray(item.originalRow.mergedRows) && 
          item.originalRow.mergedRows.length > 0) {
        // 收集此行及其合并的行的originalIndex
        const group = [item.originalIndex].concat(
          item.originalRow.mergedRows.map(subRow => subRow.originalIndex)
        );
        mergedGroups.value.push(group);
      }
    });
    
    // 更新原始数据备份
    originalRowsBackup.value = subtitleItems.value.map(item => {
      const row = { ...item.originalRow };
      row.originalIndex = item.originalIndex;
      return row;
    });
    
    // 添加额外的子行到备份中
    let additionalRows = [];
    originalRowsBackup.value.forEach((row) => {
      if (row.isMerged && Array.isArray(row.mergedRows) && row.mergedRows.length > 0) {
        row.mergedRows.forEach(subRow => {
          if (!originalRowsBackup.value.some(r => r.originalIndex === subRow.originalIndex)) {
            additionalRows.push({ ...subRow, isMerged: false });
          }
        });
      }
    });
    
    if (additionalRows.length > 0) {
      originalRowsBackup.value = [...originalRowsBackup.value, ...additionalRows];
    }
  }

  /**
   * 应用AI分组结果到当前字幕列表
   * @param {Array} groupingResult - AI返回的分组结果（二维内容数组）
   */
  function applyGroupingResult(groupingResult) {
    if (!Array.isArray(groupingResult) || groupingResult.length === 0) {
      console.error('无效的AI分组结果');
      return;
    }
    console.log('--- applyGroupingResult START ---');
    console.log(`接收到 ${groupingResult.length} 个分组`);
    try {
      mergedGroups.value = [];
      const newSubtitleItems = [];
      groupingResult.forEach((group, groupIndex) => {
        if (!Array.isArray(group) || group.length === 0) {
          console.warn(`跳过无效分组 ${groupIndex}`);
          return;
        }
        // group 是内容数组
        const matchedItems = group.map(content => findOriginalItemByContent(content)).filter(Boolean);
        if (matchedItems.length === 1) {
          newSubtitleItems.push(createSimpleSubtitleItem(matchedItems[0]));
        } else if (matchedItems.length > 1) {
          newSubtitleItems.push(createMergedSubtitleItemByContent(matchedItems));
        } else {
          // 没有匹配到原始项，创建简单项
          newSubtitleItems.push({
            id: 0,
            content: group.join('\n'),
            isMerged: group.length > 1,
            originalIndex: groupIndex,
            rowIndex: groupIndex + 1,
            originalRow: { description: group.join('\n'), isMerged: group.length > 1 }
          });
        }
      });
      if (newSubtitleItems.length > 0) {
        subtitleItems.value = newSubtitleItems;
        renumberItems();
      }
      updateBackupAndGroups();
    } catch (error) {
      console.error('应用AI分组结果时出错:', error);
    }
    console.log('--- applyGroupingResult END ---');
  }

  // 新增：通过内容查找原始项
  function findOriginalItemByContent(content) {
    return subtitleItems.value.find(item => item.content === content || item.description === content)
      || originalRowsBackup.value.find(row => row.description === content)
      || rawSrtRows.value.find(row => row.description === content);
  }

  // 新增：通过内容组装合并项
  function createMergedSubtitleItemByContent(items) {
    const containerItem = items[0];
    const mergedItem = {
      id: 0,
      content: items.map(item => item.content || item.description || '').join('\n'),
      isMerged: true,
      originalIndex: containerItem.originalIndex,
      rowIndex: containerItem.index,
      startTime: undefined,
      endTime: undefined,
      duration: undefined,
      originalRow: {
        description: '',
        isMerged: true,
        mergedRows: [],
        originalState: null
      }
    };
    mergedItem.originalRow.originalState = JSON.parse(JSON.stringify({
      originalIndex: containerItem.originalIndex,
      index: containerItem.index,
      description: containerItem.content || containerItem.description || '',
      startTime: containerItem.startTime,
      endTime: containerItem.endTime,
      duration: containerItem.duration,
      isMerged: false
    }));
    items.forEach(item => {
      mergedItem.originalRow.mergedRows.push(JSON.parse(JSON.stringify({
        originalIndex: item.originalIndex,
        index: item.index,
        description: item.content || item.description || '',
        startTime: item.startTime,
        endTime: item.endTime,
        duration: item.duration,
        isMerged: false
      })));
    });
    mergedItem.originalRow.description = mergedItem.content;
    updateMergedItemTimeRange(mergedItem);
    return mergedItem;
  }

  // 新增：创建简单字幕项（内容分组模式）
  function createSimpleSubtitleItem(originalItem) {
    const desc = originalItem.content || originalItem.description || '';
    return {
      id: 0, // 会在renumberItems中更新
      content: desc,
      isMerged: false,
      originalIndex: originalItem.originalIndex,
      rowIndex: originalItem.index,
      startTime: originalItem.startTime,
      endTime: originalItem.endTime,
      duration: originalItem.duration,
      originalRow: {
        ...JSON.parse(JSON.stringify(originalItem)),
        description: desc, // 保证有 description 字段
        content: desc      // 保证有 content 字段
      }
    };
  }

  return {
    subtitleItems,
    mergedGroups,
    originalRowsBackup,
    initializeItems,
    cancelAllMergedGroups,
    handleClick,
    handleRightClick,
    confirmMerge,
    renumberItems,
    exportText,
    importGroups,
    getCleanContent,
    countWords,
    applyGroupingResult
  };
} 