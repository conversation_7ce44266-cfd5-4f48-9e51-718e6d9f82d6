<template>
  <div>
    <!-- Preset Import Section (Removed as it's handled by GlobalReasoningDrawer) -->

    <!-- Preset List Section -->
    <div class="preset-list-section">
      <div
        v-if="isLoading"
        class="loading-message"
      >
        正在加载预设...
      </div>
      <div
        v-if="error"
        class="error-message"
      >
        加载预设失败: {{ error }}
      </div>
      
      <CharacterTable
        v-if="!isLoading && !error && items.length > 0"
        :items="items"
        :showActions="true"
        @edit="$emit('edit-item', $event)"
        @delete="$emit('delete-item', $event)"
      />
      <p v-if="!isLoading && items.length === 0 && !error">
        没有已加载的预设。
      </p>
    </div>

    <!-- Classification Modal (Removed as it's handled by GlobalReasoningDrawer and ClassifyUnmarkedEntriesModal) -->
  </div>
</template>

<script setup>
import CharacterTable from './CharacterTable.vue';
// import { ref } from 'vue'; // Removed ref as it's no longer used
// Removed: import { usePresetManager } from '../composables/usePresetManager.js';
// Removed: import { watch, onMounted } from 'vue';

// eslint-disable-next-line no-unused-vars
const props = defineProps({
  items: {
    type: Array,
    required: true,
    default: () => []
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: null
  }
  // Removed projectTitle prop
});

// Removed projectTitleRef

// Removed presetImportMessage and presetImportMessageType refs as message functions are removed
// const presetImportMessage = ref(''); 
// const presetImportMessageType = ref('');

// Removed showSuccessMessage, showErrorMessage, showInfoMessage functions
/*
const showSuccessMessage = (message) => {
  presetImportMessage.value = message;
  presetImportMessageType.value = 'success';
  setTimeout(() => {
    if (presetImportMessage.value === message) {
        presetImportMessage.value = '';
        presetImportMessageType.value = '';
    }
  }, 4000);
};

const showErrorMessage = (message) => {
  presetImportMessage.value = message;
  presetImportMessageType.value = 'error';
   setTimeout(() => {
    if (presetImportMessage.value === message) {
        presetImportMessage.value = '';
        presetImportMessageType.value = '';
    }
  }, 5000);
};

const showInfoMessage = (message) => {
  presetImportMessage.value = message;
  presetImportMessageType.value = 'info';
   setTimeout(() => {
    if (presetImportMessage.value === message) {
        presetImportMessage.value = '';
        presetImportMessageType.value = '';
    }
  }, 3000);
};
*/

// Removed: watch for props.projectTitle
// Removed: usePresetManager call
// Removed: onMounted and loadGlobalPresets call
// Removed: watch for showClassifyModal
// Removed: tempClassifiedLines, classifyingInProgress, classificationError refs
// Removed: triggerPresetFileInput
// Removed: handlePresetFileSelected
// Removed: submitClassification
// Removed: cancelClassification

// Removed: activateClassification
// Removed: defineExpose
</script>

<style scoped>
/* General Styles */
div {
  color: #e0def4; /* Default light text for dark theme */
}

button {
  background-color: #3e8fb0;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 0.9em;
}

button:hover {
  background-color: #4ea8c6;
}

button:disabled {
  background-color: #5c6370;
  cursor: not-allowed;
  opacity: 0.7;
}

input[type="text"],
select {
  padding: 8px;
  margin: 5px 0;
  border: 1px solid #44415a;
  background-color: #232136;
  color: #e0def4;
  border-radius: 4px;
}

.message {
  padding: 10px;
  margin-top: 10px;
  border-radius: 4px;
  font-size: 0.9em;
}
.message-success {
  background-color: #27522b; /* Dark green */
  color: #a7d7ab;
  border: 1px solid #3a7441;
}
.message-error {
  background-color: #5a2a2a; /* Dark red */
  color: #f8b4b4;
  border: 1px solid #8a4747;
}
.message-info {
  background-color: #2a3b4d; /* Dark blue */
  color: #a9c3e0;
  border: 1px solid #425a78;
}

/* Preset List Section */
.preset-list-section {
  padding: 0; 
}

.preset-list-section h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #e0def4;
  font-size: 1.1em;
  font-weight: 600;
  padding-bottom: 10px;
  border-bottom: 1px solid #44415a;
}

.loading-message,
.error-message {
  color: #f8b4b4;
  margin: 10px 0;
}
.error-message.small-error {
  font-size: 0.8em;
  margin-top: 5px;
}

/* Table Styles */
.preset-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 15px;
  background-color: #2a273f; /* Table background */
  color: #e0def4; /* Table text color */
}

.preset-table th,
.preset-table td {
  border: 1px solid #44415a; /* Dark theme border for cells */
  padding: 10px 12px;
  text-align: left;
  vertical-align: middle; /* Align content vertically in cells */
}

.preset-table th {
  background-color: #232136; /* Header background */
  font-weight: 600;
  font-size: 0.9em;
  color: #b8b5d6; /* Header text color */
}

.preset-table .column-type { width: 15%; }
.preset-table .column-name { width: 25%; }
.preset-table .column-description { width: 45%; }
.preset-table .column-actions { width: 15%; text-align: center;}

.preset-table-row:hover {
  background-color: #36334e; /* Hover color for rows */
}

.preset-table .cell-type {
  color: #908caa; 
  font-size: 0.85em;
}

.preset-table .cell-name {
  font-weight: 500;
}

.preset-table .cell-description {
  font-size: 0.9em;
  color: #b8b5d6;
  white-space: normal; 
  word-break: break-word; 
}

.preset-table .cell-actions {
  justify-content: center; 
  align-items: center;
  text-align: center; 
}

.icon-button {
  background: none;
  border: none;
  color: #908caa;
  cursor: pointer;
  padding: 4px; 
  border-radius: 4px;
  display: inline-flex; 
  align-items: center;
  justify-content: center;
  margin: 0 2px; 
}
.icon-button:hover {
  background-color: #44415a;
  color: #e0def4;
}

.edit-preset-button {
  margin-right: 5px; 
}

/* Modal styles removed as the classification modal is no longer part of this component */
</style> 