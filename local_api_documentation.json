{"version": "1.0.0", "description": "TXT2VIDEO应用程序本地文件操作API文档", "date": "2025-05-02", "api_endpoints": {"folder_operations": {"create_folder": {"url": "/api/local/create-folder", "method": "POST", "request_body": {"folderName": "要创建的文件夹名称", "base": "可选，基础目录路径"}, "response": {"success": true, "folderPath": "创建的文件夹完整路径"}, "error_response": {"error": "错误消息"}, "description": "创建新文件夹，可以指定基础目录", "used_in": ["ProjectCreateDialog.vue", "ChapterManager.vue"]}, "list_folders": {"url": "/api/local/list-folders", "method": "GET", "params": {"base": "可选，要列出的基础目录"}, "response": ["文件夹名1", "文件夹名2"], "error_response": {"error": "错误消息"}, "description": "列出指定目录下的所有文件夹", "used_in": ["HomePage.vue", "ChapterManager.vue"]}, "rename_folder": {"url": "/api/local/rename-folder", "method": "POST", "request_body": {"oldName": "原文件夹名", "newName": "新文件夹名", "base": "可选，基础目录"}, "response": {"success": true}, "error_response": {"error": "错误消息"}, "description": "重命名文件夹，特别支持项目和章节的重命名", "used_in": ["ProjectList.vue", "ChapterManager.vue"]}, "delete_folder": {"url": "/api/local/delete-folder", "method": "POST", "request_body": {"folderName": "要删除的文件夹名称", "base": "可选，基础目录"}, "response": {"success": true}, "error_response": {"error": "错误消息"}, "description": "删除指定文件夹", "used_in": ["HomePage.vue", "ChapterManager.vue"]}, "rename_chapter": {"url": "/api/local/rename-chapter", "method": "POST", "request_body": {"oldName": "原章节名", "newName": "新章节名", "projectName": "项目名称"}, "response": {"success": true}, "error_response": {"error": "错误消息"}, "description": "专门用于重命名章节的API，需要指定所属项目", "used_in": ["ChapterManager.vue"]}}, "file_operations": {"upload_file": {"url": "/api/local/upload-file", "method": "POST", "request_body": {"file": "文件数据 (FormData)", "projectTitle": "项目标题", "chapterTitle": "章节标题"}, "response": {"success": true, "filePath": "保存的文件路径"}, "error_response": {"error": "错误消息"}, "description": "上传SRT字幕文件或音频文件", "used_in": ["CreationPage.vue", "FileUploader.vue"]}, "list_files": {"url": "/api/local/list-files", "method": "GET", "params": {"path": "要列出文件的目录路径"}, "response": ["文件名1", "文件名2"], "error_response": {"error": "错误消息"}, "description": "列出指定目录中的所有文件", "used_in": ["ChapterManager.vue"]}, "delete_file": {"url": "/api/local/delete-file", "method": "POST", "request_body": {"filePath": "要删除的文件路径"}, "response": {"success": true, "message": "文件删除成功"}, "error_response": {"error": "错误消息"}, "description": "删除指定文件", "used_in": ["CreationPage.vue", "FileUploader.vue"]}}, "project_data": {"save_project_data": {"url": "/api/local/save-project-data", "method": "POST", "request_body": {"projectTitle": "项目标题", "chapterTitle": "章节标题", "data": {"srtFilePath": "字幕文件路径", "audioFilePath": "音频文件路径", "useBuiltinAudio": "是否使用内置音频", "其他数据": "项目自定义数据"}}, "response": {"success": true, "message": "项目数据保存成功"}, "error_response": {"error": "错误消息"}, "description": "保存项目的相关数据，包括文件路径和配置", "used_in": ["CreationPage.vue"]}}, "user_settings": {"save_user_settings": {"url": "/api/local/save-user-settings", "method": "POST", "request_body": {"settings": {"theme": "主题设置", "language": "语言设置", "autoSave": "自动保存设置", "llm": {"provider": "AI提供商", "defaultModel": "默认模型", "temperature": "温度设置", "apiKey": "API密钥", "localUrl": "本地模型URL"}}, "filename": "设置文件名，通常是usersettings.json"}, "response": {"success": true, "path": "保存的设置文件路径"}, "error_response": {"success": false, "error": "错误消息"}, "description": "保存用户设置到本地文件系统", "used_in": ["SettingsDialog.vue"]}, "load_user_settings": {"url": "/api/local/load-user-settings", "method": "GET", "params": {"filename": "设置文件名，通常是usersettings.json"}, "response": {"success": true, "settings": {"theme": "主题设置", "language": "语言设置", "autoSave": "自动保存设置", "llm": {"provider": "AI提供商", "defaultModel": "默认模型", "temperature": "温度设置", "apiKey": "API密钥", "localUrl": "本地模型URL"}}, "path": "设置文件路径"}, "error_response": {"success": false, "error": "错误消息"}, "description": "从本地文件系统加载用户设置", "used_in": ["SettingsDialog.vue"]}}}, "utility_functions": {"useLocalCommunication": {"description": "客户端辅助函数，用于简化与本地文件API的交互", "methods": {"createFolder": {"params": ["folderName", "base"], "returns": "Promise<ApiResponse>", "description": "创建新文件夹"}, "renameFolder": {"params": ["old<PERSON>ame", "newName", "base"], "returns": "Promise<ApiResponse>", "description": "重命名文件夹"}, "deleteFolder": {"params": ["folderName", "base"], "returns": "Promise<ApiResponse>", "description": "删除文件夹"}, "listFolders": {"params": ["base"], "returns": "Promise<string[]>", "description": "获取文件夹列表"}}, "reactive_properties": {"loading": "布尔值，表示API调用是否正在进行", "error": "API调用失败时的错误信息"}}}, "implementation_notes": {"server_side": {"middleware": "express.json() 必须配置以正确解析JSON请求体", "local_storage": "服务器使用文件系统存储数据，根目录为'draft'", "settings_storage": "用户设置存储在'userdata'目录"}, "client_side": {"development_mode": "在开发模式下，可能通过Vue代理服务器处理API请求", "electron_production": "在Electron生产模式下，需要移除所有localStorage用法，完全依赖服务器端存储"}, "common_issues": {"path_handling": "注意处理路径分隔符，避免路径穿越和安全问题", "error_handling": "客户端和服务器端都应有完善的错误处理", "async_operations": "所有文件操作都是异步的，确保正确处理Promise"}}}