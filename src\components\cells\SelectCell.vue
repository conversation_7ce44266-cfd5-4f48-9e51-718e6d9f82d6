<template>
  <div class="grid-cell select-cell">
    <div class="cell-content">
      <div class="select-controls">
        <div 
          class="select-checkbox" 
          :class="{'selected': isSelected}"
          @click="toggleSelect"
        />
        <div class="button-container">
          <button 
            class="merge-button" 
            :class="{'disabled': isFirstRow}"
            @click="mergeUp"
            :disabled="isFirstRow"
          >
            向上合并
          </button>
          <button 
            class="split-button" 
            :class="{'disabled': !isMerged, 'active': isMerged}"
            @click="splitDown"
            :disabled="!isMerged"
          >
            向下分拆
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SelectCell',
  emits: ['toggle-select', 'merge-up', 'split-down'],
  props: {
    isSelected: {
      type: Boolean,
      default: false
    },
    isFirstRow: {
      type: Boolean,
      default: false
    },
    isMerged: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    toggleSelect() {
      this.$emit('toggle-select');
    },
    mergeUp() {
      if (!this.isFirstRow) {
        this.$emit('merge-up');
      }
    },
    splitDown() {
      if (this.isMerged) {
        this.$emit('split-down');
      }
    }
  }
}
</script>

<style scoped>
.grid-cell {
  display: table-cell;
  text-align: center;
  vertical-align: top;
  border-right: 1px solid #333;
  border-bottom: 1px solid #333;
  color: #e0e0e0;
  box-sizing: border-box;
  font-size: 0.9rem;
  padding: 0;
  overflow: hidden;
}

.select-cell {
  width: 5%;
}

.cell-content {
  width: 100%;
  height: 100%;
  background-color: #252525;
  padding: 5px;
  box-sizing: border-box;
}

/* 选中列的选择控件和按钮样式 */
.select-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;
  padding: 10px 2px;
}

.select-checkbox {
  width: 20px;
  height: 20px;
  min-width: 20px;
  border: 2px solid #00BCD4;
  border-radius: 4px;
  background-color: transparent;
  cursor: pointer;
  transition: background-color 0.2s;
}

.select-checkbox.selected {
  background-color: #00BCD4;
}

.button-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.merge-button, .split-button {
  background-color: #333;
  border: none;
  color: white;
  padding: 5px;
  font-size: 0.7rem;
  border-radius: 3px;
  cursor: pointer;
  white-space: nowrap;
  transition: background-color 0.2s;
}

.merge-button:hover, .split-button:hover {
  background-color: #444;
}

.merge-button.disabled, .split-button.disabled {
  background-color: #222;
  color: #666;
  cursor: not-allowed;
}

.split-button.active {
  background-color: #2196F3;
  color: white;
}

.split-button.active:hover {
  background-color: #1E88E5;
}
</style> 