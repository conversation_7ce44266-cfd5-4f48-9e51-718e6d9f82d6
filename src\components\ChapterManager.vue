<template>
  <div class="chapter-manager">
    <div class="drawer-header">
      <div class="drawer-title">
        {{ project?.title || '未命名项目' }}
      </div>
    </div>
    
    <ChapterAddInput
      :show-add-chapter-input="showAddChapterInput"
      :new-chapter-title="newChapterTitle"
      @update:showAddChapterInput="val => showAddChapterInput = val"
      @update:newChapterTitle="val => newChapterTitle = val"
      @confirm="confirmAddChapter"
      @cancel="cancelAddChapter"
    />

    <ChapterList
      :chapters="project?.chapters"
      :editing-chapter-id="editingChapterId"
      :editing-chapter-title="editingChapterTitle"
      :chapters-files-status="chaptersFilesStatus"
      @edit="startEditChapter"
      @finish-edit="finishEditChapter"
      @delete="deleteChapter"
      @navigate="goToCreate"
    />
  </div>
</template>

<script>
import { ref, onMounted, watch } from 'vue';
import { useLocalCommunication } from '@/utils/localCommunication';
import ChapterAddInput from './ChapterAddInput.vue';
import ChapterList from './ChapterList.vue';

export default {
  name: 'ChapterManager',
  
  props: {
    project: {
      type: Object,
      required: true
    }
  },

  emits: ['update:project', 'navigate'],

  setup(props, { emit }) {

    // 只获取实际使用的函数，不再需要renameFolder因为我们使用专用API
    const { createFolder, deleteFolder, listFolders } = useLocalCommunication();
    
    const showAddChapterInput = ref(false);
    const newChapterTitle = ref('');
    const editingChapterId = ref(null);
    const editingChapterTitle = ref('');
    // 记录各章节的文件状态
    const chaptersFilesStatus = ref({});

    // 检查目录中的文件
    async function checkChapterFiles(projectTitle, chapterTitle) {
      try {
        const response = await fetch(`/api/local/list-files?path=${encodeURIComponent(`draft/${projectTitle}/${chapterTitle}`)}`);
        if (!response.ok) return { hasSrt: false, hasAudio: false };
        
        const files = await response.json();
        
        // 检查是否有SRT文件
        const hasSrt = files.some(file => file.toLowerCase().endsWith('.srt'));
        
        // 检查是否有音频文件
        const hasAudio = files.some(file => 
          file.toLowerCase().endsWith('.mp3') || 
          file.toLowerCase().endsWith('.wav') || 
          file.toLowerCase().endsWith('.m4a')
        );
        
        return { hasSrt, hasAudio };
      } catch (error) {
        console.error('检查章节文件失败:', error);
        return { hasSrt: false, hasAudio: false };
      }
    }
    
    // 加载所有章节的文件状态
    async function loadAllChaptersFilesStatus() {
      if (!props.project?.title || !props.project?.chapters) return;
      
      const projectTitle = props.project.title;
      const newStatus = {};
      
      for (const chapter of props.project.chapters) {
        const chapterTitle = chapter.title;
        const status = await checkChapterFiles(projectTitle, chapterTitle);
        newStatus[chapter.id] = status;
      }
      
      chaptersFilesStatus.value = newStatus;
    }

    // 在组件挂载和项目/章节变化时检查文件状态
    onMounted(() => {
      loadAllChaptersFilesStatus();
    });
    
    watch(() => props.project?.chapters, () => {
      loadAllChaptersFilesStatus();
    }, { deep: true });

    // 去创作功能
    function goToCreate(chapter) {
      console.log('跳转到创作页面', props.project.title, chapter.title);
      // 创建项目数据对象，确保同时传递项目名称和章节名称
      const projectData = {
        ...props.project,
        title: props.project.title, // 确保项目名称被正确传递
        currentChapter: chapter.title // 确保章节名称被正确传递
      };
      
      console.log('传递到创作页面的数据:', projectData);
      
      // 触发事件，通知父组件进行导航
      emit('navigate', 'creation', projectData);
    }

    async function confirmAddChapter() {
      if (!newChapterTitle.value.trim() || !props.project) return;
      
      try {
        if (!props.project?.title) {
          throw new Error('项目标题不存在');
        }
        
        const sanitizedTitle = newChapterTitle.value.trim().replace(/[<>:"/\\|?*]/g, '_');
        const projectTitle = props.project.title.replace(/[<>:"/\\|?*]/g, '_');
        
        console.log(`创建章节文件夹: draft/${projectTitle}/${sanitizedTitle}`);
        await createFolder(sanitizedTitle, projectTitle);
        
        const chapterFullPath = `${projectTitle}/${sanitizedTitle}`;
        
        const updatedProject = { ...props.project };
        updatedProject.chapters = updatedProject.chapters || [];
        updatedProject.chapters.push({
          title: sanitizedTitle,
          path: chapterFullPath,
          createdAt: new Date().toISOString(),
          id: Date.now() + Math.random().toString(36).slice(2, 8)
        });
        
        emit('update:project', updatedProject);
        newChapterTitle.value = '';
        showAddChapterInput.value = false;
      } catch (error) {
        console.error('创建章节失败:', error);
        alert('创建章节失败: ' + error.message);
      }
    }

    function cancelAddChapter() {
      showAddChapterInput.value = false;
      newChapterTitle.value = '';
    }

    function startEditChapter(chapter) {
      editingChapterId.value = chapter.id;
      editingChapterTitle.value = chapter.title;
    }

    async function finishEditChapter(chapter) {
      console.log('章节重命名 - 接收到更新章节:', chapter);
      // 确保来自子组件的chapter对象包含了新的title值
      const newTitle = chapter.title;
      
      if (!newTitle || newTitle.trim() === '' || newTitle === chapter.originalTitle) {
        editingChapterId.value = null;
        editingChapterTitle.value = '';
        return;
      }
      
      // 更新编辑状态，确保使用最新值
      editingChapterTitle.value = newTitle;
      
      try {
        if (!props.project?.title) {
          throw new Error('项目标题不存在');
        }
        
        // 使用来自章节对象的新标题而不是编辑框的值
        // 非常重要: 使用原始标题作为重命名的源
        const oldTitle = chapter.originalTitle || chapter.title;
        console.log('重命名源标题信息:', { 
          id: chapter.id,
          title: chapter.title,
          originalTitle: chapter.originalTitle,
          最终使用的oldTitle: oldTitle
        });
        
        // 修复正则表达式
        const sanitizedNewTitle = newTitle.trim().replace(/[<>:"/?*|]/g, '_');
        const projectTitle = props.project.title.replace(/[<>:"/?*|]/g, '_');
        
        console.log(`重命名章节文件夹 - 旧名称:`, oldTitle, `新名称:`, sanitizedNewTitle, `项目:`, projectTitle);
        
        // 重要: 章节重命名需要特别处理
        console.log(`章节重命名调用 - 旧名称: ${oldTitle}, 新名称: ${sanitizedNewTitle}, 项目: ${projectTitle}`);
        
        try {
          // 使用新的专用章节重命名 API
          console.log(`调用章节重命名 API: 旧名称=${oldTitle}, 新名称=${sanitizedNewTitle}, 项目=${projectTitle}`);
          
          // 直接调用专用章节重命名 API
          const response = await fetch('/api/local/rename-chapter', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              oldName: oldTitle,
              newName: sanitizedNewTitle,
              projectName: projectTitle
            })
          });
          
          const data = await response.json();
          console.log('章节重命名结果:', data);
          
          if (data.success) {
            // 重命名成功后更新本地UI
            const newPath = `${projectTitle}/${sanitizedNewTitle}`;
            const updatedProject = { ...props.project };
            const targetChapter = updatedProject.chapters.find(c => c.id === chapter.id);
            if (targetChapter) {
              targetChapter.title = sanitizedNewTitle;
              targetChapter.path = newPath;
            }
            emit('update:project', updatedProject);
          } else {
            throw new Error(data.error || '重命名失败');
          }
        } catch (error) {
          console.error('章节重命名出错:', error);
        }
        
        const newPath = `${projectTitle}/${sanitizedNewTitle}`;
        
        const updatedProject = { ...props.project };
        const targetChapter = updatedProject.chapters.find(c => c.id === chapter.id);
        if (targetChapter) {
          targetChapter.title = sanitizedNewTitle;
          targetChapter.path = newPath;
        }
        emit('update:project', updatedProject);
      } catch (error) {
        console.error('重命名章节失败:', error);
        alert('重命名章节失败: ' + error.message);
      }
      
      editingChapterId.value = null;
      editingChapterTitle.value = '';
    }

    async function deleteChapter(chapter) {
      if (!confirm(`确定要删除章节"${chapter.title}"吗？`)) return;
      
      try {
        if (!props.project?.title) {
          throw new Error('项目标题不存在');
        }
        
        const projectTitle = props.project.title.replace(/[<>:"/\\|?*]/g, '_');
        
        console.log(`删除章节文件夹: draft/${projectTitle}/${chapter.title}`);
        await deleteFolder(chapter.title, projectTitle);
        
        const updatedProject = { ...props.project };
        const index = updatedProject.chapters.findIndex(c => c.id === chapter.id);
        if (index > -1) {
          updatedProject.chapters.splice(index, 1);
        }
        emit('update:project', updatedProject);
      } catch (error) {
        console.error('删除章节失败:', error);
        alert('删除章节失败: ' + error.message);
      }
    }

    async function fetchChapters() {
      if (!props.project?.title) return;
      try {
        const folders = await listFolders(props.project.title);
        const chapters = folders.map(name => ({
          title: name,
          path: `${props.project.title}/${name}`,
          createdAt: '',
          id: name
        }));
        const updatedProject = { ...props.project, chapters };
        emit('update:project', updatedProject);
      } catch (e) {
        // 可选：错误处理
      }
    }

    onMounted(fetchChapters);
    watch(() => props.project?.title, fetchChapters);

    return {
      showAddChapterInput,
      newChapterTitle,
      editingChapterId,
      editingChapterTitle,
      chaptersFilesStatus,
      confirmAddChapter,
      cancelAddChapter,
      startEditChapter,
      finishEditChapter,
      deleteChapter,
      goToCreate
    };
  },
  components: {
    ChapterAddInput,
    ChapterList
  }
};
</script>

<style scoped>
.chapter-manager {
  padding: 0 !important; /* 完全移除内边距 */
  display: flex !important; 
  flex-direction: column !important;
  width: 100% !important; 
  flex-grow: 1 !important;
  box-sizing: border-box !important;
  /* 调试边框移除 */
  /* border: 2px solid red !important; */
}

.drawer-header {
  width: 100% !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  margin-bottom: 2.2rem !important;
}

.drawer-title {
  font-size: 2.2rem;
  font-weight: 900;
  color: #f5c2e7;
  letter-spacing: 2.5px;
  text-align: center;
  background: linear-gradient(90deg, #f5c2e7 10%, #34ace0 50%, #a23a5a 90%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  text-shadow: 0 2px 16px #a23a5a55, 0 1px 2px #34ace099;
  filter: drop-shadow(0 0 8px #34ace099);
  margin-bottom: 0.5rem;
  transition: font-size 0.2s, text-shadow 0.2s;
}

.chapter-actions {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 0.5rem;
  height: 100%;
  margin: 0;
  padding: 0;
}

.chapter-input {
  flex: 1 1 220px;
  min-width: 180px;
  max-width: 260px;
  padding: 0.7rem 1.2rem;
  border-radius: 22px;
  border: 1.5px solid #444;
  background: #181825;
  color: #fff;
  font-size: 1.08rem;
  outline: none;
  height: 44px;
  box-sizing: border-box;
  transition: border-color 0.2s;
}

.chapter-input:focus {
  border-color: #a23a5a;
}

.confirm-chapter-btn, .cancel-chapter-btn {
  min-width: 70px;
  padding: 0 1.5rem;
  height: 44px;
  border-radius: 22px;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px 0 #22223b22;
  letter-spacing: 1px;
  border: none;
  cursor: pointer;
  transition: background 0.18s, color 0.18s, box-shadow 0.18s;
}

.confirm-chapter-btn {
  background: linear-gradient(90deg, #29908b 0%, #a23a5a 100%);
  color: #fff;
}
.confirm-chapter-btn:hover {
  background: linear-gradient(90deg, #a23a5a 0%, #29908b 100%);
  color: #fff;
  box-shadow: 0 6px 24px 0 #a23a5a33;
}

.cancel-chapter-btn {
  background: #292945;
  color: #b5bfe2;
  margin-left: 0.1rem;
}
.cancel-chapter-btn:hover {
  background: #393552;
  color: #f5c2e7;
  box-shadow: 0 6px 24px 0 #39355233;
}

@media (max-width: 600px) {
  .chapter-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 0.7rem;
  }
  .chapter-input, .confirm-chapter-btn, .cancel-chapter-btn {
    width: 100%;
    min-width: 0;
    box-sizing: border-box;
  }
}

/* 重新设计表格为网格布局 */
.chapter-list-table {
  width: 100% !important;
  margin-top: 1rem !important;
  overflow: visible !important;
  display: flex !important;
  flex-direction: column !important;
  /* 调试边框移除 */
  /* border: 2px solid blue !important; */
}

/* 表头 */
.chapter-list-header {
  display: grid !important;
  grid-template-columns: 60% 20% 20% !important; /* 列宽比例 */
  width: 100% !important;
  background: #393552 !important;
  color: #fff !important;
  font-weight: 700 !important;
  font-size: 1.03rem !important;
  /* 调试边框移除 - 表头不需要单独的边框，因为它属于 .chapter-list-grid 的一部分 */
}

/* 表行 */
.chapter-list-row {
  display: grid !important;
  grid-template-columns: 60% 20% 20% !important; /* 与表头相同的列宽比例 */
  width: 100% !important;
  background: #232136 !important;
  /* 调试边框移除 */
  /* border: 2px solid green !important; */
}

.chapter-list-row:nth-child(even) {
  background: #292945 !important;
}

.chapter-top-actions {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.6rem;
  width: 100%;
  box-sizing: border-box;
  margin: 1.2rem 0 0.5rem 0;
}

.add-chapter-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(90deg, #29908b 0%, #a23a5a 100%);
  color: #fff;
  border: none;
  border-radius: 24px;
  padding: 0.7rem 1.5rem;
  font-size: 1.08rem;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 2px 12px 0 #22223b22;
  transition: all 0.18s;
  letter-spacing: 1px;
  min-width: 120px;
  max-width: 200px;
}

.add-chapter-btn i {
  font-size: 1.3em;
}

.add-chapter-btn:hover {
  background: linear-gradient(90deg, #a23a5a 0%, #29908b 100%);
  color: #fff;
  transform: scale(1.04);
  box-shadow: 0 4px 16px 0 #a23a5a33;
}

.chapter-input {
  flex: 1 1 180px;
  min-width: 120px;
  max-width: 220px;
  margin: 0 0.2rem;
}

.confirm-chapter-btn, .cancel-chapter-btn {
  min-width: 60px;
  padding: 0 1rem;
  height: 38px;
  border-radius: 19px;
  font-size: 0.98rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 4px 0 #22223b22;
  letter-spacing: 1px;
  border: none;
  cursor: pointer;
  transition: background 0.18s, color 0.18s, box-shadow 0.18s;
}

@media (max-width: 800px) {
  .chapter-top-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
  .add-chapter-btn, .chapter-input, .confirm-chapter-btn, .cancel-chapter-btn {
    width: 100%;
    min-width: 0;
    max-width: 100%;
    box-sizing: border-box;
    font-size: 0.98rem;
    padding: 0.7rem 1rem;
  }
}

</style>