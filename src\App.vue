<template>
  <div id="app">
    <header class="header">
      <div class="header-left">
        <div class="header-logo">
          <i class="ri-film-line" />
        </div>
        <div class="header-title">
          有声漫画生成器
        </div>
      </div>
      <div class="header-right">
        <div
          class="llm-status"
          :class="{'has-llm': currentLLM}"
        >
          <span class="llm-indicator" />
          <span class="llm-name">{{ currentLLM || '无LLM' }}</span>
        </div>
        <button
          class="header-button"
          title="设置"
          @click="showSettings = true"
        >
          <i class="ri-settings-3-line" />
        </button>
        <button
          class="header-button"
          title="帮助"
        >
          <i class="ri-question-line" />
        </button>
      </div>
    </header>

    <main class="main-content">
      <HomePage
        v-if="currentView === 'home'"
        @navigate="navigate"
      />
      <CreationPage
        v-if="currentView === 'creation'"
        @navigate="navigate"
        :project="currentProject"
      />
      <ContentCreationStudio
        v-if="currentView === 'studio'"
        @navigate="navigate"
        :project="currentProject"
        @open-adjust-shots-drawer="openAdjustShotsDrawer"
        @open-global-reasoning-drawer="openGlobalReasoningDrawer"
        ref="contentCreationStudio"
      />
    </main>

    <!-- 设置对话框 -->
    <SettingsDialog
      v-model="showSettings"
      @save-settings="handleSaveSettings"
    />

    <!-- 调整分镜抽屉 -->
    <AdjustShotsDrawer
      v-if="showAdjustShotsDrawer"
      :show="showAdjustShotsDrawer"
      :rows="adjustShotsRows"
      @update:show="showAdjustShotsDrawer = $event"
      @update-rows="handleAdjustShotsResult"
    />

    <!-- 全局推理抽屉 -->
    <GlobalReasoningDrawer
      :show="showGlobalReasoningDrawer"
      :initial-text="globalReasoningText"
      :project-title="projectTitle"
      :chapter-title="chapterTitle"
      @update:show="showGlobalReasoningDrawer = $event"
      @close-drawer="handleCloseGlobalDrawer"
    />
  </div>
</template>

<script>
import HomePage from './components/HomePage.vue'
import CreationPage from './components/CreationPage.vue'
import ContentCreationStudio from './components/ContentCreationStudio.vue'
import SettingsDialog from './components/SettingsDialog.vue'
import AdjustShotsDrawer from './components/AdjustShotsDrawer.vue'
import GlobalReasoningDrawer from './components/GlobalReasoningDrawer.vue'
import { provideProjectContext } from './composables/useProjectContext.js'

export default {
  name: 'App',
  components: {
    HomePage,
    CreationPage,
    ContentCreationStudio,
    SettingsDialog,
    AdjustShotsDrawer,
    GlobalReasoningDrawer
  },
  data() {
    return {
      currentView: 'home',
      currentProject: null,
      projects: [],
      showDrawer: false,
      showSettings: false,
      appSettings: null,
      currentLLM: '', // 当前选择的LLM名称

      // 调整分镜抽屉相关数据
      showAdjustShotsDrawer: false,
      adjustShotsRows: [], // 当前行数据
      projectData: null, // 对ContentCreationStudio组件的引用

      // 全局推理抽屉相关数据
      showGlobalReasoningDrawer: false,
      globalReasoningText: '',
      formattedReasoningText: '', // 将保留这个字段但不使用选择器
      projectTitle: '',
      chapterTitle: '',

      // loading state for projects
      isLoadingProjects: false,
      toast: {
      },
    }
  },
  methods: {
    navigate(view, data) {
      console.log('App导航被调用:', view);

      if (view === 'home') {
        console.log('导航返回主页');
        this.currentView = view;
        this.showDrawer = false;
        return;
      }

      if (view === 'novel-chapters') {
        console.log('导航到小说章节页面');
        this.currentView = view;
        this.showDrawer = true;
        if (data) {
          console.log('小说数据:', data.id, data.title);
          this.currentProject = data;
        }
        return;
      }

      if (view === 'chapters') {
        console.log('导航到章节页面:', data);
        this.currentView = 'novel-chapters';
        this.showDrawer = true;
        if (data) {
          console.log('小说数据:', data.id, data.title);
          this.currentProject = data;
        }
        return;
      }

      if (view === 'creation' && this.currentView === 'studio') {
        console.log('从Studio导航回CreationPage，保留项目数据');
        if (data) {
          console.log('传递的项目数据:', data.id);
          this.currentProject = data;
        }
      }

      // 如果导航到studio，使用路由导航并保存项目数据
      if (view === 'studio' && data) {
        console.log('导航到ContentCreationStudio，使用路由导航并保存项目数据');

        // 获取项目和章节信息
        this.projectTitle = data.title || '';
        this.chapterTitle = data.currentChapter || '';

        // 使用路由导航
        if (this.projectTitle && this.chapterTitle) {
          // 保存项目数据到project-data.json文件中
          this.saveProjectData(data);

          // 使用路由导航
          this.$router.push({
            name: 'studio',
            query: {
              project: this.projectTitle,
              chapter: this.chapterTitle
            }
          });
        }
      }

      // 设置当前视图
      this.currentView = view;

      // 更新项目数据（除非是导航回主页）
      if (data) {
        console.log('更新当前项目数据:', data.title);
        this.currentProject = data;
      }
    },
    async loadProjects() {
      try {
        // 这里将来会从Electron API获取项目数据
        this.projects = [];
      } catch (error) {
        console.error('加载项目失败:', error);
      }
    },
    handleSaveSettings(settings) {
      console.log('[App.vue handleSaveSettings] Received settings for handleSaveSettings:', JSON.stringify(settings.llm));
      this.appSettings = settings;

      // 更新当前LLM显示
      if (settings && settings.llm) {
        const originalDefaultModel = settings.llm.defaultModel;
        if (settings.llm.provider === 'google' && settings.llm.defaultModel) {
          // 从模型ID中获取更友好的显示名称
          const modelId = settings.llm.defaultModel;
          if (modelId.includes('gemini')) {
            this.currentLLM = `Gemini ${modelId.replace('gemini-', '').replace(/-/g, ' ')}`;
          } else {
            this.currentLLM = modelId;
          }
        } else if (settings.llm.provider === 'openrouter' && settings.llm.defaultModel) {
          // 处理OpenRouter模型
          const parts = settings.llm.defaultModel.split('/');
          this.currentLLM = parts.length > 1 ? parts[1] : settings.llm.defaultModel;
        } else if (settings.llm.provider && settings.llm.defaultModel) {
          // 其他提供商
          this.currentLLM = settings.llm.defaultModel;
        } else {
          this.currentLLM = '未配置模型';
        }
        console.log(`[App.vue handleSaveSettings] Original defaultModel: "${originalDefaultModel}", Provider: "${settings.llm.provider}", Calculated currentLLM: "${this.currentLLM}"`);
      } else {
        this.currentLLM = '设置或LLM配置缺失';
        console.log(`[App.vue handleSaveSettings] Settings or settings.llm missing. currentLLM set to: "${this.currentLLM}"`);
      }
    },
    async loadSavedSettings() {
      try {
        const response = await fetch('/api/local/load-user-settings?filename=usersettings.json');
        if (response.ok) {
          const result = await response.json();
          if (result.success && result.settings) {
            console.log('[App.vue loadSavedSettings] Loaded settings from API:', JSON.stringify(result.settings.llm));
            this.handleSaveSettings(result.settings);
          }
        }
      } catch (error) {
        console.error('加载设置失败:', error);
      }
    },

    // 保存项目数据到文件
    saveProjectData(projectData) {
      if (!projectData || !projectData.data) {
        console.error('无法保存项目数据：数据无效');
        return;
      }

      console.log('保存项目数据到文件...');

      // 构建保存数据
      const saveData = {
        projectTitle: projectData.title || '',
        chapterTitle: projectData.currentChapter || '',
        data: projectData.data || {}
      };

      // 发送保存请求
      fetch('/api/local/save-project-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(saveData)
      })
      .then(response => {
        if (!response.ok) {
          throw new Error(`保存失败: ${response.status}`);
        }
        return response.json();
      })
      .then(result => {
        console.log('项目数据保存成功', result);
      })
      .catch(error => {
        console.error('保存项目数据失败:', error);
      });
    },

    // 处理从AdjustShotsDrawer返回的数据
    handleAdjustShotsResult(updatedRows) {
      console.log('App接收到来自AdjustShotsDrawer的更新行数据，行数:', updatedRows.length);

      // 关闭抽屉
      this.showAdjustShotsDrawer = false;

      // 如果ContentCreationStudio组件存在，调用其updateRowsFromDrawer方法
      const studioComponent = this.$refs.contentCreationStudio;
      if (studioComponent && typeof studioComponent.updateRowsFromDrawer === 'function') {
        console.log('调用ContentCreationStudio的updateRowsFromDrawer方法');
        studioComponent.updateRowsFromDrawer(updatedRows);
      } else {
        console.warn('ContentCreationStudio组件不存在或未提供updateRowsFromDrawer方法');

        // 如果无法调用方法，尝试直接更新项目数据
        if (this.projectData && this.projectData.data) {
          console.log('直接更新App中的项目数据');
          this.projectData.data.rows = updatedRows;

          // 保存项目数据
          this.saveProjectData(this.projectData);
        }
      }
    },

    // 打开调整分镜抽屉
    openAdjustShotsDrawer(rows) {
      console.log('App.vue - openAdjustShotsDrawer - 打开调整分镜抽屉，接收到的行数:', rows?.length);

      if (!rows || rows.length === 0) {
        console.error('App.vue - openAdjustShotsDrawer - 接收到的行数据为空');
        this.adjustShotsRows = [];
        this.showAdjustShotsDrawer = true;
        return;
      }

      console.log('App.vue - openAdjustShotsDrawer - 接收到的第一行数据示例:', JSON.stringify(rows[0], null, 2));

      // 缓存处理过的数据
      const processedRows = rows.map(row => {
        // 确保每行都有所需属性
        return {
          ...row,
          originalIndex: row.originalIndex || row.index,
          isMerged: row.isMerged !== undefined ? row.isMerged : false,
          mergedRows: (row.isMerged && !row.mergedRows) ? [] : row.mergedRows || []
        };
      });

      // 传递给抽屉的处理后行数据
      console.log('传递给抽屉的处理后行数据:', processedRows.length);
      this.adjustShotsRows = processedRows;
      this.showAdjustShotsDrawer = true;
    },

    // 格式化时间辅助方法
    formatTime(seconds) {
      const h = Math.floor(seconds / 3600);
      const m = Math.floor((seconds % 3600) / 60);
      const s = Math.floor(seconds % 60);
      return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
    },

    // 格式化持续时间
    formatDuration(seconds) {
      const minutes = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${minutes}:${secs.toString().padStart(2, '0')}`;
    },

    // 打开全局推理抽屉
    openGlobalReasoningDrawer(rows, formattedText) {
      console.log('[App.vue - openGlobalReasoningDrawer] 打开全局推理抽屉，接收到的行数:', rows?.length);

      // 从 URL 或当前状态获取项目和章节信息
      if (this.$route.query.project && this.$route.query.chapter) {
        this.projectTitle = this.$route.query.project;
        this.chapterTitle = this.$route.query.chapter;
      } else if (this.currentProject) {
        this.projectTitle = this.currentProject.title || '';
        this.chapterTitle = this.currentProject.currentChapter || '';
      }

      console.log('[App.vue - openGlobalReasoningDrawer] 当前项目章节信息:', {
        projectTitle: this.projectTitle,
        chapterTitle: this.chapterTitle
      });

      // 尝试从ContentCreationStudio组件获取项目数据
      const studioComponent = this.$refs.contentCreationStudio;
      if (studioComponent && studioComponent.projectData) {
        console.log('[App.vue - openGlobalReasoningDrawer] 从ContentCreationStudio获取项目数据');
        this.projectData = studioComponent.projectData;
        this.updateContextProjectData(this.projectData);
      } else {
        console.warn('[App.vue - openGlobalReasoningDrawer] 无法从ContentCreationStudio获取项目数据');
      }

      // 处理格式化文本 - 直接使用格式化文本
      if (formattedText) {
        this.formattedReasoningText = formattedText;
        console.log('[App.vue - openGlobalReasoningDrawer] 接收到格式化文本，长度:', formattedText.length);
        // 直接设置全局推理文本为格式化文本
        this.globalReasoningText = formattedText;
      } else {
        // 如果没有格式化文本，则使用原始文本处理方式
        if (rows && rows.length > 0) {
          // 从传入的rows中提取文本
          this.globalReasoningText = rows.map(r => {
            // 优先使用text，如果没有则使用description，两者都没有则返回空字符串
            return (r.text || r.description || '').trim();
          }).filter(text => text).join('\n');

          console.log('[App.vue - openGlobalReasoningDrawer] 生成的原始文本预览:',
            this.globalReasoningText.substring(0, 200) + (this.globalReasoningText.length > 200 ? '...' : ''));
        } else {
          console.warn('[App.vue - openGlobalReasoningDrawer] 传入的rows为空或未定义');
          this.globalReasoningText = ''; // 设置为空字符串
        }
      }

      this.showGlobalReasoningDrawer = true;
    },

    handleGlobalReasoningResult(result) {
      // 此方法似乎不再被GlobalReasoningDrawer触发，可以考虑移除
      console.log('[App.vue] Received global reasoning result:', result);
      // 这里可以根据需要处理结果，例如更新项目预设等
    },

    handleCloseGlobalDrawer() {
      // 此方法似乎不再被GlobalReasoningDrawer触发，可以考虑移除
      console.log('[App.vue] Received global reasoning drawer close event');
      // 这里可以根据需要处理事件，例如重置全局推理文本等
    },
  },
  mounted() {
    // 初始化时加载项目列表
    this.loadProjects();
    // 加载设置并更新LLM状态
    this.loadSavedSettings();

    // 延迟获取项目数据，确保ContentCreationStudio组件已经加载
    this.$nextTick(() => {
      setTimeout(() => {
        const studioComponent = this.$refs.contentCreationStudio;
        if (studioComponent && studioComponent.projectData) {
          console.log('[App.vue - mounted] 从ContentCreationStudio获取项目数据');
          this.projectData = studioComponent.projectData;
          this.updateContextProjectData(this.projectData);
        }
      }, 1000); // 延迟1秒确保数据加载完成
    });
  },
  setup() {
    // 提供项目上下文
    const {
      setProjectTitle: updateContextProjectTitle,
      setChapterTitle: updateContextChapterTitle,
      setProjectData: updateContextProjectData
    } = provideProjectContext();
    // 返回方法供选项式 API 使用
    return {
      updateContextProjectTitle,
      updateContextChapterTitle,
      updateContextProjectData
    };
  },
  watch: {
    // 监视 projectTitle 的变化
    projectTitle(newTitle) {
      // 更新项目上下文中的 projectTitle
      this.updateContextProjectTitle(newTitle);
    },
    // 监视 chapterTitle 的变化
    chapterTitle(newTitle) {
      // 更新项目上下文中的 chapterTitle
      this.updateContextChapterTitle(newTitle);
    },
    // 监视 projectData 的变化
    projectData: {
      handler(newProjectData) {
        // 更新项目上下文中的 projectData
        this.updateContextProjectData(newProjectData);
      },
      deep: true
    }
  }
}
</script>

<style>
/* 导入样式 */
@import url('https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css');

/* 基础样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  background-color: #1e1e2e;
  color: #cdd6f4;
  height: 100vh;
  overflow: hidden;
}

#app {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem 1.5rem;
  height: 60px;
  background-color: #181825;
  border-bottom: 1px solid #313244;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.header-logo {
  font-size: 1.5rem;
}

.header-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #cba6f7;
}

.header-right {
  display: flex;
  gap: 0.8rem;
}

.header-button {
  width: 32px;
  height: 32px;
  background-color: #313244;
  border: none;
  font-size: 1rem;
  color: #cdd6f4;
  cursor: pointer;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.header-button:hover {
  background-color: #45475a;
  transform: translateY(-2px);
}

/* 主要内容区 */
.main-content {
  height: calc(100vh - 60px);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 2rem;
}

/* LLM状态指示器样式 */
.llm-status {
  display: flex;
  align-items: center;
  padding: 0 12px;
  font-size: 14px;
  color: #cdd6f4;
  margin-right: 10px;
}

.llm-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #666;
  margin-right: 6px;
  display: inline-block;
}

.llm-status.has-llm {
  color: #00c853;
}

.llm-status.has-llm .llm-indicator {
  background-color: #00c853;
  box-shadow: 0 0 5px #00c853, 0 0 10px #00c853;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 5px #00c853;
  }
  50% {
    box-shadow: 0 0 10px #00c853, 0 0 15px #00c853;
  }
  100% {
    box-shadow: 0 0 5px #00c853;
  }
}

/* 导入其他CSS样式 */
</style>
