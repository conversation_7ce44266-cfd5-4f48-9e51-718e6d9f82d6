<template>
  <div class="grid-cell operation-cell">
    <div class="cell-content">
      <div class="operation-container">
        <!-- 操作按钮区域 -->
        <div class="operation-buttons">
          <button 
            class="operation-button highlight"
            @click="redrawImage"
          >
            重绘本镜
          </button>
          <button 
            class="operation-button"
            @click="inferPrompt"
          >
            推理提示词
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OperationCell',
  emits: ['redraw-image', 'infer-prompt'],
  methods: {
    redrawImage() {
      this.$emit('redraw-image');
    },
    inferPrompt() {
      this.$emit('infer-prompt');
    }
  }
}
</script>

<style scoped>
.grid-cell {
  display: table-cell;
  text-align: center;
  vertical-align: top;
  border-right: 1px solid #333;
  border-bottom: 1px solid #333;
  color: #e0e0e0;
  box-sizing: border-box;
  font-size: 0.9rem;
  padding: 0;
  overflow: hidden;
}

.operation-cell {
  width: 10%;
  border-right: none;
}

.cell-content {
  width: 100%;
  height: 100%;
  padding: 5px;
  box-sizing: border-box;
}

/* 操作列样式 */
.operation-container {
  width: 100%;
  height: 100%;
  background-color: #1e1e1e;
  border-radius: 2px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 8px;
}

.operation-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.operation-button {
  width: 100%;
  background-color: #222;
  border: 1px solid #333;
  color: #aaa;
  padding: 6px 0;
  border-radius: 3px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.operation-button:hover {
  background-color: #2d2d2d;
  border-color: #444;
  color: #e0e0e0;
}

.operation-button.highlight {
  background-color: #2a2a2a;
  color: #3e8fb0;
  border: 1px solid #3e8fb0;
  font-weight: normal;
}

.operation-button.highlight:hover {
  background-color: #3e8fb0;
  color: #fff;
}
</style> 