<template>
  <div class="grid-cell image-cell">
    <div class="image-wrapper">
      <div class="image-top-bar">
        <div class="top-bar-buttons">
          <button
            class="top-button"
            :class="{'locked': isLocked}"
            @click="toggleLock"
          >
            {{ isLocked ? '已锁定' : '上锁' }}
          </button>
          <button
            class="top-button clear-button"
            @click="clearImage"
          >
            清空
          </button>
        </div>
      </div>
      <div
        class="display-window"
        @click="selectImage"
      >
        <img
          v-if="imageSrc && imageSrc !== 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'"
          :src="imageSrc"
          :alt="imageAlt"
          class="preview-image"
        >
        <div v-else class="placeholder-text">点击选择图片</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ImageCell',
  emits: ['select-image', 'toggle-lock', 'clear-image'],
  props: {
    imageSrc: {
      type: String,
      default: ''
    },
    imageAlt: {
      type: String,
      default: ''
    },
    isLocked: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    selectImage() {
      this.$emit('select-image');
    },
    toggleLock() {
      this.$emit('toggle-lock');
    },
    clearImage() {
      this.$emit('clear-image');
    }
  }
}
</script>

<style scoped>
.grid-cell {
  display: table-cell;
  text-align: center;
  vertical-align: top;
  border-right: 1px solid #333;
  border-bottom: 1px solid #333;
  color: #e0e0e0;
  box-sizing: border-box;
  font-size: 0.9rem;
  padding: 0;
  overflow: hidden;
}

.image-cell {
  width: 12%;
}

.image-wrapper {
  width: 100%;
  height: 100%;
  background-color: #252525;
  display: flex;
  flex-direction: column;
}

.image-top-bar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 30px;
  padding: 2px 5px;
  background-color: #1e1e1e;
}

.top-bar-buttons {
  display: flex;
  gap: 5px;
}

.top-button {
  background-color: #333;
  border: none;
  color: white;
  padding: 3px 8px;
  border-radius: 3px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.top-button:hover {
  background-color: #444;
}

.top-button.locked {
  background-color: #3e8fb0;
  color: white;
}

.clear-button:hover {
  background-color: #c53030;
}

.display-window {
  flex: 1;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: box-shadow 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin: 5px;
  border: 1px dashed #333;
}

.display-window:hover {
  box-shadow: 0 0 0 2px rgba(62, 143, 176, 0.3);
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  background-color: #1a1a1a;
}

.placeholder-text {
  color: #555;
  font-size: 0.8rem;
}
</style> 