<template>
  <div class="character-analyzer-demo">
    <h2 class="demo-title">
      角色色分析器示�?    </h2>
    
    <LLMCommunicator
      title="角色色分析�?
      placeholder="输入小说或剧本片段，AI将提取角色色信�?.."
      button-text="分析角色色"
      analyze-type="character-analysis"
      :default-prompt="characterPromptTemplate"
      :show-advanced="true"
      :show-raw-output="false"
      :textarea-rows="10"
      @processing-complete="handleAnalysisComplete"
    >
      <!-- 自定义的结果显示模板 -->
      <template #formatted-result="{ data }">
        <div
          v-if="data && data.characters && data.characters.length > 0"
          class="character-results"
        >
          <div
            class="character-card"
            v-for="(character, index) in data.characters"
            :key="index"
          >
            <div class="character-header">
              <span class="character-name">{{ character.name || '未命名角色? }}</span>
            </div>
            <div class="character-details">
              <div class="character-detail">
                <span class="detail-label">视觉特征:</span>
                <span class="detail-value">{{ character.visualTraits || '未提�? }}</span>
              </div>
              <div
                v-if="character.personality"
                class="character-detail"
              >
                <span class="detail-label">性格:</span>
                <span class="detail-value">{{ character.personality }}</span>
              </div>
              <div
                v-if="character.role"
                class="character-detail"
              >
                <span class="detail-label">角色色定位:</span>
                <span class="detail-value">{{ character.role }}</span>
              </div>
            </div>
          </div>
        </div>
        <div
          v-else-if="data && data.setting"
          class="setting-result"
        >
          <h3>背景设定</h3>
          <div class="setting-detail">
            <div class="setting-name">
              {{ data.setting.name || data.setting.era || '未命名设�? }}
            </div>
            <p class="setting-description">
              {{ data.setting.description || '未提供描�? }}
            </p>
          </div>
        </div>
        <div
          v-else
          class="no-data-message"
        >
          暂无有效分析结果，请尝试提供更多文本内容
        </div>
      </template>
      
      <!-- 自定义按�?-->
      
      
      <!-- 自定义的结果操作按钮 -->
      
    </LLMCommunicator>
  </div>
</template>



 

      
      <!-- 自定义按�?-->
      
      
      <!-- 自定义的结果操作按钮 -->
      
    </LLMCommunicator>
  </div>
</template>



 
