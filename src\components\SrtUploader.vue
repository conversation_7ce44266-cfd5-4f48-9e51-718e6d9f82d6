<template>
  <div class="srt-uploader">
    <div 
      class="upload-area" 
      @click="triggerFileInput" 
      @drop.prevent="handleFileDrop" 
      @dragover.prevent="onDragOver"
      @dragleave.prevent="onDragLeave"
      :class="{ 'drag-over': isDragOver }"
    >
      <input 
        type="file" 
        ref="fileInput" 
        accept=".srt" 
        @change="handleFileUpload" 
        class="hidden-input" 
      >
      <div class="upload-placeholder">
        <div class="upload-icon">
          <i class="icon-upload" />
        </div>
        <div class="upload-text">
          点击或拖拽SRT文件到此处上�?        </div>
        <div class="upload-hint">
          支持.srt格式字幕文件
        </div>
      </div>
    </div>
    
    <div
      v-if="isProcessing"
      class="processing-indicator"
    >
      <div class="spinner" />
      <div class="processing-text">
        正在处理SRT文件，请稍�?..
      </div>
    </div>

    <div
      v-if="errorMessage"
      class="error-message"
    >
      {{ errorMessage }}
    </div>
  </div>
</template>



 




 
