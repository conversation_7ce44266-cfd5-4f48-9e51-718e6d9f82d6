<template>
  <div class="steps-navigation">
    <div class="navigation-left">
      <div class="step-back">
        <button
          class="back-button"
          @click="goBack"
        >
          <i class="ri-arrow-left-line" /> 回主页
        </button>
        <div class="cool-title-bar">
          <span class="cool-project-name">{{ projectTitle }}</span>
          <span
            v-if="chapterTitle"
            class="cool-separator"
          >-</span>
          <span
            v-if="chapterTitle"
            class="cool-chapter-name"
          >{{ chapterTitle }}</span>
        </div>
      </div>
    </div>
    
    <div class="navigation-center">
      <div class="steps-container">
        <div
          class="step"
          :class="{'active': currentStep === 'import', 'current-step': currentStep === 'import'}"
          @click="tryNavigateToStep('import')"
        >
          <span class="step-number">1</span>
          <span class="step-text">导入素材</span>
        </div>
        <div
          class="step"
          :class="{'active': currentStep === 'edit', 'current-step': currentStep === 'edit'}"
          @click="tryNavigateToStep('edit')"
        >
          <span class="step-number">2</span>
          <span class="step-text">编辑生成</span>
        </div>
        <div
          class="step"
          :class="{'active': currentStep === 'preview', 'current-step': currentStep === 'preview'}"
          @click="tryNavigateToStep('preview')"
        >
          <span class="step-number">3</span>
          <span class="step-text">预览作品</span>
        </div>
      </div>
    </div>
    
    <div class="navigation-right">
      <button 
        v-if="showExportButton"
        class="export-button" 
        @click="handleExportButtonClick"
      >
        导出导入
      </button>
      <button 
        class="next-step-button" 
        :class="{'disabled': isNextButtonDisabled}" 
        @click="handleNextButtonClick"
      >
        下一步
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StepsNavigationBar',
  props: {
    projectTitle: {
      type: String,
      default: ''
    },
    chapterTitle: {
      type: String,
      default: ''
    },
    currentStep: {
      type: String,
      required: true
    },
    // 添加这些属性，用于检查是否上传了必要文件
    hasSrtFile: {
      type: Boolean,
      default: false
    },
    hasAudioFile: {
      type: Boolean,
      default: false
    },
    useBuiltinAudio: {
      type: Boolean,
      default: false
    },
    // 添加控制导出按钮显示的属性
    showExportButton: {
      type: Boolean,
      default: false
    },
    // 控制项目标题是否可编辑
    allowTitleEdit: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      // 已不需要编辑相关的状态
    }
  },
  created() {
    // 不再需要初始化编辑状态
  },
  watch: {
    // 不再需要监听章节标题变化
  },
  computed: {
    // 是否有章节部分
    hasChapterPart() {
      return this.chapterTitle !== "";
    },
    
    // 添加调试日志
    projectDebug() {
      console.log('StepsNavigationBar 接收到的数据:', {
        projectTitle: this.projectTitle,
        chapterTitle: this.chapterTitle
      });
      return true;
    },
    
    isNextButtonDisabled() {
      // 如果当前步骤是import，且SRT文件未上传，则禁用按钮
      // 注意：我们现在只需要SRT文件，音频文件变为可选
      if (this.currentStep === 'import') {
        return !this.hasSrtFile;
      }
      return false;
    }
  },
  methods: {
    goBack() {
      console.log('StepsNavigationBar: 触发返回事件');
      this.$emit('go-back');
    },
    goToNextStep() {
      this.$emit('next-step');
    },
    tryNavigateToStep(stepName) {
      // 尝试导航到特定步骤
      if ((stepName === 'edit' || stepName === 'preview') && !this.hasSrtFile) {
        // 检查SRT文件是否已上传
        this.$emit('show-error', '请先上传SRT字幕文件才能继续');
        return;
      }
      
      // 通知父组件导航到指定步骤
      this.$emit('navigate-to-step', stepName);
    },
    handleNextButtonClick() {
      if (this.isNextButtonDisabled) {
        // 如果按钮禁用，显示错误信息
        this.$emit('show-error', '请先上传SRT字幕文件才能继续');
      } else {
        // 否则正常进入下一步
        this.goToNextStep();
      }
    },
    handleExportButtonClick() {
      this.$emit('export-import');
    }
  }
}
</script>

<style scoped>
/* 步骤导航 */
.steps-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #111119;
  border-bottom: 1px solid #222233;
  height: 60px;
}

.navigation-left,
.navigation-right {
  flex: 1;
  display: flex;
}

.navigation-left {
  justify-content: flex-start;
}

.navigation-right {
  justify-content: flex-end;
  gap: 10px;
}

.navigation-center {
  flex: 3;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.6rem;
}

.step-back {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #cdd6f4;
  font-size: 0.9rem;
  max-width: 100%;
  overflow: hidden;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 100px;
  height: 38px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0 18px;
  font-size: 15px;
  background-color: #2dc0f0;
  color: #111119;
  font-weight: bold;
  white-space: nowrap;
  gap: 8px;
}

.back-button:hover {
  background-color: #3ad7ff;
  transform: translateY(-2px);
}

.back-button i {
  font-size: 20px;
  color: #cdd6f4;
}

.steps-container {
  display: flex;
  gap: 2rem;
  position: relative;
}

.steps-container::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #333344;
  z-index: 0;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
  gap: 0.5rem;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.step:hover {
  transform: translateY(-2px);
}

.step:hover .step-number {
  box-shadow: 0 0 8px rgba(45, 192, 240, 0.6);
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: #333344;
  color: #a6adc8;
  font-weight: bold;
}

.step.active .step-number {
  background-color: #2dc0f0;
  color: #111119;
}

.step-text {
  font-size: 0.85rem;
  color: #a6adc8;
}

.step.active .step-text {
  color: #cdd6f4;
  font-weight: bold;
}

.next-step-button {
  padding: 0.6rem 1.2rem;
  background-color: #2dc0f0;
  color: #111119;
  border: none;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
}

.next-step-button:hover {
  background-color: #3ad7ff;
  transform: translateY(-2px);
}

.next-step-button.disabled {
  background-color: #45475a;
  color: #9c9db3;
  cursor: not-allowed;
  transform: none;
}

.next-step-button.disabled:hover {
  background-color: #45475a;
  transform: none;
}

/* 酷炫项目-章节标题样式 */
.cool-title-bar {
  display: flex;
  align-items: center;
  font-size: 2rem;
  font-weight: 900;
  letter-spacing: 1px;
  background: none;
  margin-left: 1.5rem;
  user-select: text;
}

.cool-project-name {
  background: linear-gradient(90deg, #3366ff 0%, #2dc0f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  text-shadow: 0 2px 8px rgba(45, 192, 240, 0.6);
  filter: drop-shadow(0 0 10px rgba(51, 102, 255, 0.7));
  font-weight: 900;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 2.2rem;
}

.cool-separator {
  font-size: 2.2rem;
  font-weight: 700;
  margin: 0 4px;
  background: linear-gradient(90deg, #3366ff 0%, #2dc0f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  text-shadow: 0 2px 8px rgba(45, 192, 240, 0.6);
  filter: drop-shadow(0 0 10px rgba(51, 102, 255, 0.7));
}

.cool-chapter-name {
  background: linear-gradient(90deg, #3366ff 0%, #2dc0f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  text-shadow: 0 2px 8px rgba(45, 192, 240, 0.6);
  filter: drop-shadow(0 0 10px rgba(51, 102, 255, 0.7));
  font-weight: 900;
  max-width: 550px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@keyframes cool-glow {
  0% { filter: drop-shadow(0 0 8px rgba(51, 102, 255, 0.7)); }
  100% { filter: drop-shadow(0 0 15px rgba(45, 192, 240, 0.8)); }
}

@media (max-width: 768px) {
  .step-back {
    gap: 0.5rem;
    max-width: 100%;
  }
  
  .cool-title-bar {
    margin-left: 0.5rem;
  }
  
  .cool-project-name {
    max-width: 100px;
  }
  
  .cool-chapter-name {
    max-width: 150px;
  }
  
  .navigation-left {
    max-width: 45%;
  }
}

.project-name-input, .chapter-name-input {
  background: rgba(45, 192, 240, 0.05);
  border: 1px solid #2dc0f0;
  border-radius: 4px;
  padding: 2px 6px;
  color: #fff;
  font-size: 0.95rem;
  min-width: 120px;
  max-width: 200px;
}

.hint-edit {
  animation: pulse-text 2s infinite;
}

@keyframes pulse-text {
  0% {
    color: #f5c2e7;
    text-shadow: 0 0 0 rgba(45, 192, 240, 0);
    font-weight: normal;
    transform: scale(1);
  }
  50% {
    color: #2dc0f0;
    text-shadow: 0 0 12px rgba(45, 192, 240, 0.8);
    font-weight: bold;
    transform: scale(1.05);
  }
  100% {
    color: #f5c2e7;
    text-shadow: 0 0 0 rgba(45, 192, 240, 0);
    font-weight: normal;
    transform: scale(1);
  }
}

/* 当前步骤文字闪烁效果 */
.step.current-step .step-text {
  animation: pulse-step-text 3s infinite;
}

/* 当前步骤数字圆圈闪烁效果 */
.step.current-step .step-number {
  animation: pulse-step-number 1.5s infinite;
  box-shadow: 0 0 0 rgba(45, 192, 240, 0.7);
}

@keyframes pulse-step-text {
  0% {
    color: #cdd6f4;
    text-shadow: 0 0 0 rgba(45, 192, 240, 0);
  }
  50% {
    color: #2dc0f0;
    text-shadow: 0 0 5px rgba(45, 192, 240, 0.7);
  }
  100% {
    color: #cdd6f4;
    text-shadow: 0 0 0 rgba(45, 192, 240, 0);
  }
}

@keyframes pulse-step-number {
  0% {
    background-color: #2dc0f0;
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(45, 192, 240, 0.8);
  }
  50% {
    background-color: #3ad7ff;
    transform: scale(1.15);
    box-shadow: 0 0 10px 3px rgba(45, 192, 240, 0.9);
  }
  100% {
    background-color: #2dc0f0;
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(45, 192, 240, 0.8);
  }
}

.export-button {
  padding: 0.6rem 1.2rem;
  background-color: #333344;
  color: #cdd6f4;
  border: none;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
}

.export-button:hover {
  background-color: #45475a;
  transform: translateY(-2px);
}
</style>