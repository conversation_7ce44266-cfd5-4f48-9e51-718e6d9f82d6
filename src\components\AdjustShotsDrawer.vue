<template>
  <BaseDrawer
    :show="show"
    @update:show="val => $emit('update:show', val)"
    :drawer-width="'50vw'"
  >
    <div class="adjust-shots-drawer">
      <div class="drawer-header-row">
        <h2 class="drawer-title">调整合并字幕</h2>
        <div class="drawer-description">点击字幕左键合并，右键分拆。完成后点击"确认合并"。</div>
        <div class="action-buttons">
          <button class="action-button" @click="importGroups" title="导入外部修改好的分组文本">
            <i class="ri-file-text-line"></i> 导入分组
          </button>
          <button class="action-button" @click="exportText" title="导出当前所有文本到剪贴板">
            <i class="ri-clipboard-line"></i> 导出文本
          </button>
          <button class="action-button" @click="cancelAllMergedGroups" title="取消所有合并分组">
            <i class="ri-delete-bin-line"></i> 取消分组
          </button>
          <button class="action-button ai-button" @click="showAIGrouping = true" title="使用AI智能分组字幕">
            <i class="ri-robot-line"></i> AI分组
          </button>
        </div>
        <div class="status-bar">
          <div class="status-text" v-if="mergedGroups.length > 0">
            已合并 {{ mergedGroups.length }} 组，当前显示 {{ subtitleItems.length }} 行
          </div>
          <div class="status-text" v-else>
            尚未进行任何合并
          </div>
        </div>
      </div>
        <div class="subtitle-list">
          <div class="subtitle-list-header">
          <div class="column-header">字幕</div>
          </div>
          <div class="subtitle-items-container">
            <div class="subtitle-items">
              <div
                v-for="(item, index) in subtitleItems"
                :key="item.id"
                :class="['subtitle-item', { 'merged': item.isMerged }]"
                @click="handleClick(index)"
                @contextmenu.prevent="handleRightClick(index)"
              >
              <div class="item-serial">{{ item.id }}</div>
              <div class="item-content" :title="item.content">{{ item.content }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="drawer-footer">
        <div class="operation-buttons">
          <button class="operation-button cancel" @click="$emit('update:show', false)">取消</button>
          <button class="operation-button confirm" @click="onConfirmMerge">确认合并</button>
        </div>
      </div>
    </div>
  </BaseDrawer>

  <!-- AI分组组件，放在BaseDrawer外部渲染 -->
  <Teleport to="body">
    <AIGroupingModal
      v-if="showAIGrouping"
      :show="showAIGrouping"
      :subtitles="subtitleItems"
      @update:show="showAIGrouping = $event"
      @apply-grouping="applyAIGrouping"
    />
  </Teleport>

  <!-- 验证错误弹窗 -->
  <Teleport to="body">
    <ValidationErrorModal
      v-if="showValidationError"
      :show="showValidationError"
      :error-type="validationErrorType"
      :error-title="validationErrorTitle"
      :error-message="validationErrorMessage"
      :validation-details="validationErrorDetails"
      @update:show="showValidationError = $event"
    />
  </Teleport>
</template>

<script>
import BaseDrawer from './BaseDrawer.vue';
import AIGroupingModal from './AIGroupingModal.vue';
import ValidationErrorModal from './ValidationErrorModal.vue';
import { ref, watch, onMounted } from 'vue';
import { useGroupingManager } from '../composables/useGroupingManager';
import { useGroupingOperations } from '../composables/useGroupingOperations';

export default {
  name: 'AdjustShotsDrawer',

  components: {
    BaseDrawer,
    AIGroupingModal,
    ValidationErrorModal
  },

  props: {
    show: {
      type: Boolean,
      default: false
    },
    rows: {
      type: Array,
      default: () => []
    }
  },

  emits: ['update:show', 'update-rows'],

  setup(props, { emit }) {
    // 状态
    const subtitleItems = ref([]);
    const mergedGroups = ref([]);
    const rawSrtRows = ref([]);
    const hasChanges = ref(false);
    const showAIGrouping = ref(false);

    // 验证错误弹窗状态
    const showValidationError = ref(false);
    const validationErrorType = ref('general');
    const validationErrorTitle = ref('');
    const validationErrorMessage = ref('');
    const validationErrorDetails = ref(null);

    // 使用分组管理器
    const groupingManager = useGroupingManager();

    // 使用分组操作
    const groupingOperations = useGroupingOperations();

    /**
     * 显示验证错误弹窗
     */
    function showValidationErrorModal(type, title, message, details = null) {
      validationErrorType.value = type;
      validationErrorTitle.value = title;
      validationErrorMessage.value = message;
      validationErrorDetails.value = details;
      showValidationError.value = true;
    }

    // 监听抽屉显示变化
    watch(() => props.show, (newVal) => {
      if (newVal) {
        initializeItems();
      }
    });

    // 初始化时检查是否显示
    onMounted(() => {
      if (props.show) {
        initializeItems();
      }
    });

    /**
     * 初始化字幕项
     * 完全重写版本，更加可靠地处理各种情况
     */
    async function initializeItems() {
      console.log('开始初始化字幕项...');

      if (!props.rows || props.rows.length === 0) {
        subtitleItems.value = [];
        mergedGroups.value = [];
        rawSrtRows.value = [];
        console.warn('没有可用的行数据');
        return;
      }

      // 深拷贝行数据
      const sourceRows = JSON.parse(JSON.stringify(props.rows));
      console.log('源数据行数:', sourceRows.length);

      // 检查是否有任何合并行
      const hasMergedRows = sourceRows.some(row => row.isMerged);
      console.log('是否有合并行:', hasMergedRows ? '是' : '否');

      // 重置rawSrtRows，确保每次打开抽屉时都重新初始化
      rawSrtRows.value = [];

      // 尝试从多个来源获取原始SRT数据
      let originalDataFound = false;

      // 1. 首先尝试从localStorage加载（这是最可靠的来源，因为我们在确认合并时保存了它）
      const localStorageData = loadFromLocalStorage();
      if (localStorageData && localStorageData.length > 0) {
        console.log('成功从localStorage加载原始数据，行数:', localStorageData.length);
        rawSrtRows.value = localStorageData;
        originalDataFound = true;
      }

      // 2. 如果localStorage中没有数据，且当前数据没有合并行，直接使用当前数据
      if (!originalDataFound && !hasMergedRows) {
        console.log('当前数据没有合并行，直接用作原始数据');
        rawSrtRows.value = sourceRows.map(row => ({
          ...row,
          isMerged: false,
          mergedRows: undefined,
          mergedRowsCount: undefined,
          originalState: undefined
        }));
        originalDataFound = true;
      }

      // 3. 如果前两种方法都失败，尝试从当前数据提取未合并状态
      if (!originalDataFound) {
        console.log('尝试从当前数据提取未合并状态');
        const extractedRows = extractUnmergedRows(sourceRows);

        if (extractedRows && extractedRows.length > 0) {
          console.log('成功从当前数据提取未合并状态，行数:', extractedRows.length);
          rawSrtRows.value = extractedRows;
          originalDataFound = true;

          // 保存到localStorage以备将来使用
          saveToLocalStorage(extractedRows);
        }
      }

      // 4. 如果前三种方法都失败，尝试从SRT文件加载
      if (!originalDataFound) {
        console.log('尝试从SRT文件加载原始数据');
        const srtData = await loadOriginalSrtData();

        if (srtData && srtData.length > 0) {
          console.log('成功从SRT文件加载原始数据，行数:', srtData.length);
          rawSrtRows.value = srtData;
          originalDataFound = true;

          // 保存到localStorage以备将来使用
          saveToLocalStorage(srtData);
        }
      }

      // 5. 如果所有方法都失败，创建一个基本的未合并版本
      if (!originalDataFound || rawSrtRows.value.length === 0) {
        console.warn('无法获取原始未合并数据，将创建基本版本');
        rawSrtRows.value = sourceRows.map((row, index) => ({
          index: index + 1,
          originalIndex: row.originalIndex !== undefined ? row.originalIndex : index,
          description: row.description || '',
          startTime: row.startTime,
          endTime: row.endTime,
          duration: row.duration,
          isMerged: false,
          mergedRows: undefined,
          mergedRowsCount: undefined,
          originalState: undefined
        }));
      }

      // 确保rawSrtRows中的所有行都是未合并状态
      rawSrtRows.value.forEach(row => {
        row.isMerged = false;
        row.mergedRows = undefined;
        row.mergedRowsCount = undefined;
        row.originalState = undefined;
      });

      console.log('rawSrtRows初始化完成，行数:', rawSrtRows.value.length);

      // 转换为字幕项
      subtitleItems.value = sourceRows.map((row, index) => {
        return {
          id: index + 1,
          content: row.description || '',
          isMerged: row.isMerged || false,
          originalIndex: row.originalIndex !== undefined ? row.originalIndex : index,
          rowIndex: row.index !== undefined ? row.index : index + 1,
          originalRow: JSON.parse(JSON.stringify(row)), // 深拷贝，避免引用问题
          startTime: row.startTime,
          endTime: row.endTime,
          duration: row.duration
        };
      });

      // 收集合并组信息
      collectMergedGroups();

      // 重置变更状态
      hasChanges.value = false;

      console.log('字幕项初始化完成，当前项数:', subtitleItems.value.length);
    }

    /**
     * 保存原始SRT数据到localStorage
     */
    function saveToLocalStorage(data) {
      try {
        // 获取项目和章节信息
        const urlParams = new URLSearchParams(window.location.search);
        const projectTitle = urlParams.get('project') || localStorage.getItem('currentProject');
        const chapterTitle = urlParams.get('chapter') || localStorage.getItem('currentChapter');

        if (!projectTitle || !chapterTitle || !data || data.length === 0) {
          return false;
        }

        // 保存原始SRT数据
        const key = `original_srt_data_${projectTitle}_${chapterTitle}`;
        localStorage.setItem(key, JSON.stringify(data));
        console.log('已保存原始SRT数据到localStorage，行数:', data.length);
        return true;
      } catch (error) {
        console.error('保存原始SRT数据到localStorage失败:', error);
        return false;
      }
    }

    /**
     * 从可能包含合并项的数据中提取所有未合并状态的行
     * 完全重写版本，更加可靠地处理各种情况
     */
    function extractUnmergedRows(rows) {
      console.log('开始提取未合并状态行，输入行数:', rows?.length || 0);

      // 如果没有输入行，返回空数组
      if (!rows || rows.length === 0) return [];

      // 创建一个映射来存储所有行，按originalIndex索引
      const allRowsMap = new Map();

      // 第一步：收集所有行，包括合并行中的子行
      function collectAllRows(row, isTopLevel = false) {
        // 跳过无效行
        if (!row) return;

        // 确保行有originalIndex
        if (row.originalIndex === undefined) {
          row.originalIndex = row.index !== undefined ? row.index - 1 : 0;
        }

        // 创建基本行对象（始终设置为未合并状态）
        const baseRow = {
          description: row.description || '',
          originalIndex: row.originalIndex,
          index: row.index || (row.originalIndex + 1),
          startTime: row.startTime,
          endTime: row.endTime,
          duration: row.duration,
          isMerged: false, // 重要：始终设置为未合并状态
          // 复制其他可能有用的属性
          tags: row.tags ? (Array.isArray(row.tags) ? [...row.tags] : [row.tags]) : undefined,
          keywords: row.keywords,
          imageSrc: row.imageSrc,
          imageAlt: row.imageAlt,
          isImageLocked: row.isImageLocked,
          thumbnails: row.thumbnails ? (Array.isArray(row.thumbnails) ? [...row.thumbnails] : []) : undefined
        };

        // 如果是顶级行或尚未收集此originalIndex，则添加到映射
        if (isTopLevel || !allRowsMap.has(baseRow.originalIndex)) {
          allRowsMap.set(baseRow.originalIndex, baseRow);
          console.log(`收集行: originalIndex=${baseRow.originalIndex}, description=${baseRow.description.substring(0, 20)}...`);
        }

        // 如果是合并行，处理其子行
        if (row.isMerged && row.mergedRows && row.mergedRows.length > 0) {
          console.log(`处理合并行的子行，数量:`, row.mergedRows.length);

          // 处理所有子行
          row.mergedRows.forEach(subRow => {
            collectAllRows(subRow);
          });

          // 如果有originalState，也处理它
          if (row.originalState) {
            collectAllRows(row.originalState);
          }
        }
      }

      // 处理所有顶级行
      rows.forEach(row => {
        collectAllRows(row, true);
      });

      // 第二步：从映射中提取所有行并排序
      const result = Array.from(allRowsMap.values());

      // 按startTime排序，确保顺序与原始SRT文件一致
      result.sort((a, b) => {
        // 首先按时间排序（如果有）
        if (a.startTime !== undefined && b.startTime !== undefined) {
          return a.startTime - b.startTime;
        }
        // 然后按originalIndex排序
        return a.originalIndex - b.originalIndex;
      });

      // 重新编号
      result.forEach((row, index) => {
        row.index = index + 1;
      });

      console.log(`提取完成，共 ${result.length} 行未合并状态数据`);
      return result;
    }

    /**
     * 收集合并组信息
     */
    function collectMergedGroups() {
      mergedGroups.value = [];

      subtitleItems.value.forEach(item => {
        if (item.isMerged && item.originalRow &&
            Array.isArray(item.originalRow.mergedRows) &&
            item.originalRow.mergedRows.length > 0) {
          // 收集此行及其合并的行的originalIndex
          const group = [item.originalIndex].concat(
            item.originalRow.mergedRows.map(subRow => subRow.originalIndex)
          );
          mergedGroups.value.push(group);
        }
      });
    }

    /**
     * 重新编号所有项
     */
    function renumberItems() {
      subtitleItems.value.forEach((item, index) => {
        item.id = index + 1;
      });
    }

    /**
     * 处理点击事件（合并）
     */
    function handleClick(index) {
      // 实现"向上合并"逻辑：当点击某一行时，将该行合并到上一行

      // 检查是否是第一行（第一行没有上一行可合并）
      if (index === 0) {
        console.log('这是第一行，无法向上合并');
        return;
      }

      // 确定目标索引（上一行）和源索引（当前行）
      const targetIndex = index - 1; // 上一行作为目标（保留）
      const sourceIndex = index; // 当前行作为源（被合并）

      // 合并当前行到上一行
      const result = groupingManager.mergeItems(subtitleItems.value, targetIndex, sourceIndex);

      if (result.success) {
        subtitleItems.value = result.subtitleItems;
        renumberItems();
        collectMergedGroups();
        hasChanges.value = true;
        console.log(`已将第 ${sourceIndex + 1} 行合并到第 ${targetIndex + 1} 行`);
      } else {
        console.error('合并失败:', result.error);
      }

      // 清除所有选中状态
      subtitleItems.value.forEach(item => {
        item.isSelected = false;
      });
    }

    /**
     * 处理右键点击事件（分拆）
     */
    function handleRightClick(index) {
      // 检查是否是合并项
      if (!subtitleItems.value[index].isMerged) {
        return;
      }

      // 分拆合并项
      const result = groupingManager.splitItem(subtitleItems.value, index);

      if (result.success) {
        subtitleItems.value = result.subtitleItems;
        renumberItems();
        collectMergedGroups();
        hasChanges.value = true;
      } else {
        console.error('分拆失败:', result.error);
      }
    }

    /**
     * 取消所有合并分组
     */
    async function cancelAllMergedGroups() {
      console.log('开始执行取消所有分组操作...');

      // 检查是否有原始未合并数据
      if (!rawSrtRows.value || rawSrtRows.value.length === 0) {
        console.warn('没有可用的原始未合并数据，尝试从当前数据提取');

        // 尝试从当前数据提取
        rawSrtRows.value = extractUnmergedRows(props.rows);

        // 如果仍然无法获取原始数据，尝试从SRT文件重新加载
        if (rawSrtRows.value.length === 0) {
          console.warn('从当前数据提取失败，尝试从SRT文件重新加载原始数据');

          // 尝试从SRT文件重新加载
          const srtData = await loadOriginalSrtData();

          if (srtData && srtData.length > 0) {
            console.log('成功从SRT文件加载原始数据，行数:', srtData.length);
            rawSrtRows.value = srtData;
          } else {
            console.error('无法获取原始未合并数据，取消分组失败');
            alert('无法取消分组：找不到原始未合并数据。请尝试重新打开抽屉或刷新页面。');
            return;
          }
        }
      }

      // 调用分组管理器的取消分组方法
      const result = groupingManager.cancelAllMergedGroups(subtitleItems.value, rawSrtRows.value);

      if (result.success) {
        // 更新字幕项和合并组
        subtitleItems.value = result.subtitleItems;
        mergedGroups.value = [];
        hasChanges.value = true;

        console.log('成功取消所有分组，恢复到原始状态');
      } else {
        console.error('取消所有分组失败:', result.error);
        alert(`取消分组失败: ${result.error}`);
      }
    }

    /**
     * 从localStorage加载原始SRT数据
     * @returns {Array|null} 原始SRT数据或null
     */
    function loadFromLocalStorage() {
      try {
        // 获取项目和章节信息
        const urlParams = new URLSearchParams(window.location.search);
        const projectTitle = urlParams.get('project') || localStorage.getItem('currentProject');
        const chapterTitle = urlParams.get('chapter') || localStorage.getItem('currentChapter');

        if (!projectTitle || !chapterTitle) {
          console.error('无法获取项目或章节信息');
          return null;
        }

        // 构建localStorage键
        const key = `original_srt_data_${projectTitle}_${chapterTitle}`;

        // 从localStorage获取数据
        const dataStr = localStorage.getItem(key);
        if (!dataStr) {
          console.warn('localStorage中没有找到原始SRT数据');
          return null;
        }

        // 解析数据
        const data = JSON.parse(dataStr);
        if (!Array.isArray(data) || data.length === 0) {
          console.warn('localStorage中的原始SRT数据无效');
          return null;
        }

        console.log('成功从localStorage加载原始SRT数据，行数:', data.length);
        return data;
      } catch (error) {
        console.error('从localStorage加载原始SRT数据失败:', error);
        return null;
      }
    }

    /**
     * 从SRT文件加载原始数据
     * 这是一个后备方法，当无法从现有数据中提取原始状态时使用
     */
    async function loadOriginalSrtData() {
      try {
        // 获取项目和章节信息
        const urlParams = new URLSearchParams(window.location.search);
        const projectTitle = urlParams.get('project') || localStorage.getItem('currentProject');
        const chapterTitle = urlParams.get('chapter') || localStorage.getItem('currentChapter');

        if (!projectTitle || !chapterTitle) {
          console.error('无法获取项目或章节信息');
          return null;
        }

        // 构建SRT文件路径
        const srtPath = `draft/${projectTitle}/${chapterTitle}/${chapterTitle}.srt`;
        console.log('尝试从SRT文件加载原始数据，路径:', srtPath);

        // 读取SRT文件内容
        const response = await fetch(`/api/local/read-file?path=${encodeURIComponent(srtPath)}`);

        if (!response.ok) {
          console.error('无法读取SRT文件:', response.status);
          return null;
        }

        const result = await response.json();

        if (!result.success || !result.content) {
          console.error('SRT文件内容无效');
          return null;
        }

        // 解析SRT内容
        const srtContent = result.content;
        const parsedRows = parseSrtContent(srtContent);

        if (!parsedRows || parsedRows.length === 0) {
          console.error('SRT解析结果为空');
          return null;
        }

        console.log('成功解析SRT内容，共', parsedRows.length, '条字幕');
        return parsedRows;
      } catch (error) {
        console.error('加载原始SRT数据失败:', error);
        return null;
      }
    }

    /**
     * 解析SRT内容
     * @param {String} srtContent SRT文件内容
     * @returns {Array} 解析后的行数据
     */
    function parseSrtContent(srtContent) {
      if (!srtContent) return [];

      try {
        // 按空行分割字幕块
        const subtitleBlocks = srtContent.split(/\r?\n\r?\n/).filter(block => block.trim());

        // 解析每个字幕块
        return subtitleBlocks.map((block, index) => {
          const lines = block.split(/\r?\n/).filter(line => line.trim());

          // 至少需要3行：序号、时间码、文本
          if (lines.length < 3) return null;

          // 解析时间码行
          const timeCodeLine = lines[1];
          const timeCodeMatch = timeCodeLine.match(/(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})/);

          if (!timeCodeMatch) return null;

          // 转换时间码为秒
          const startTime = convertTimeToSeconds(timeCodeMatch[1]);
          const endTime = convertTimeToSeconds(timeCodeMatch[2]);

          // 提取文本内容（可能有多行）
          const description = lines.slice(2).join('\n');

          // 创建行对象
          return {
            index: index + 1,
            originalIndex: index,
            description,
            startTime,
            endTime,
            duration: endTime - startTime,
            isMerged: false
          };
        }).filter(Boolean); // 过滤掉解析失败的项
      } catch (error) {
        console.error('解析SRT内容失败:', error);
        return [];
      }
    }

    /**
     * 将SRT时间码转换为秒
     * @param {String} timeCode 时间码，格式为 "00:00:00,000"
     * @returns {Number} 秒数
     */
    function convertTimeToSeconds(timeCode) {
      const parts = timeCode.split(/[:,]/);
      if (parts.length !== 4) return 0;

      const hours = parseInt(parts[0], 10);
      const minutes = parseInt(parts[1], 10);
      const seconds = parseInt(parts[2], 10);
      const milliseconds = parseInt(parts[3], 10);

      return hours * 3600 + minutes * 60 + seconds + milliseconds / 1000;
    }

    /**
     * 导出文本
     */
    async function exportText() {
      console.log('开始导出文本，当前字幕项数量:', subtitleItems.value?.length || 0);

      if (!subtitleItems.value || subtitleItems.value.length === 0) {
        console.error('导出文本失败: 没有可用的字幕内容');
        alert('没有可用的字幕内容可导出');
        return;
      }

      try {
        const result = await groupingOperations.exportSubtitleText(subtitleItems.value);

        if (result.success) {
          console.log('导出文本成功:', result.message);
          alert(result.message);
        } else {
          console.error('导出文本失败:', result.error);
          alert(`导出文本失败: ${result.error}`);
        }
      } catch (error) {
        console.error('导出文本过程中出错:', error);
        alert(`导出文本过程中出错: ${error.message || '未知错误'}`);
      }
    }

    /**
     * 导入分组
     */
    function importGroups() {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.txt';
      input.onchange = handleFileSelection;
      input.click();
    }

    /**
     * 处理文件选择
     */
    function handleFileSelection(event) {
      const file = event.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target.result;
        const result = groupingOperations.parseImportedContent(content, subtitleItems.value);

        if (result.success) {
          subtitleItems.value = result.subtitleItems;
          collectMergedGroups();
          hasChanges.value = true;
        } else {
          console.error('导入分组失败:', result.error);
          showValidationErrorModal(
            'import',
            '导入分组失败',
            result.error,
            result.validationDetails
          );
        }
      };
      reader.readAsText(file);
    }

    /**
     * 应用AI分组结果
     */
    function applyAIGrouping(groupingResult) {
      console.log('接收到AI分组结果:', typeof groupingResult, groupingResult);

      // 检查是否是包含rawText的对象（从AIGroupingModal传来的原始文本格式）
      if (groupingResult && groupingResult.rawText) {
        console.log('处理AI返回的原始文本格式');
        // 使用parseImportedContent处理原始文本
        const result = groupingOperations.parseImportedContent(groupingResult.rawText, subtitleItems.value);

        if (result.success) {
          subtitleItems.value = result.subtitleItems;
          collectMergedGroups();
          hasChanges.value = true;
        } else {
          console.error('解析AI分组文本失败:', result.error);
          showValidationErrorModal(
            'ai',
            'AI分组失败',
            result.error,
            result.validationDetails
          );
        }
      } else {
        // 处理数组格式的分组结果
        const result = groupingOperations.applyAIGroupingResult(subtitleItems.value, groupingResult);

        if (result.success) {
          subtitleItems.value = result.subtitleItems;
          collectMergedGroups();
          hasChanges.value = true;
        } else {
          console.error('应用AI分组结果失败:', result.error);
          showValidationErrorModal(
            'ai',
            'AI分组失败',
            result.error,
            result.validationDetails
          );
        }
      }

      showAIGrouping.value = false;
    }

    /**
     * 确认合并
     */
    function onConfirmMerge() {
      console.log('执行确认合并操作...');

      // 在确认合并前进行最终验证
      const originalSubtitles = rawSrtRows.value || [];
      if (originalSubtitles.length > 0) {
        // 计算原始字幕的总字符数（不含标点）
        const originalCharCount = originalSubtitles.reduce((total, item) => {
          const content = item.description || '';
          const cleanContent = content.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '');
          return total + cleanContent.length;
        }, 0);

        // 计算当前字幕的总字符数（不含标点）
        const currentCharCount = subtitleItems.value.reduce((total, item) => {
          const content = item.content || '';
          const cleanContent = content.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '');
          return total + cleanContent.length;
        }, 0);

        // 检查字符数是否匹配（允许5%的误差）
        const charDifference = Math.abs(originalCharCount - currentCharCount);
        const allowedDifference = Math.max(5, originalCharCount * 0.05); // 至少5个字符或5%的误差

        if (charDifference > allowedDifference) {
          console.error('字符数验证失败:', {
            原始字符数: originalCharCount,
            当前字符数: currentCharCount,
            差异: charDifference,
            允许差异: allowedDifference
          });

          showValidationErrorModal(
            'merge',
            '合并验证失败',
            `字符数不匹配：原始字幕有${originalCharCount}个字符，当前字幕有${currentCharCount}个字符，差异${charDifference}个字符超出允许范围。为保护数据完整性，无法进行合并。`,
            {
              isValid: false,
              errors: [`字符数差异过大：${charDifference}个字符（允许范围：${allowedDifference}个字符）`],
              warnings: [],
              matchedCount: 0,
              unmatchedCount: charDifference
            }
          );
          return; // 阻止合并操作
        }
      }

      // 将字幕项转换回行数据
      const resultRows = subtitleItems.value.map(item => {
        const row = {
          ...JSON.parse(JSON.stringify(item.originalRow)), // 深拷贝，避免引用问题
          description: item.content,
          isMerged: item.isMerged,
          index: item.rowIndex,
          originalIndex: item.originalIndex
        };

        // 确保其他属性正确传递
        row.startTime = item.startTime;
        row.endTime = item.endTime;
        row.duration = item.duration;

        return row;
      });

      // 检查是否有合并行
      const hasMergedRows = resultRows.some(row => row.isMerged);

      // 无论是否有合并行，都保存原始SRT数据
      // 这确保了即使用户先合并后又全部取消，我们仍然有原始数据
      if (rawSrtRows.value && rawSrtRows.value.length > 0) {
        console.log('确认合并：保存原始SRT数据，行数:', rawSrtRows.value.length);

        // 确保rawSrtRows中的所有行都是未合并状态
        const cleanRows = rawSrtRows.value.map(row => ({
          ...row,
          isMerged: false,
          mergedRows: undefined,
          mergedRowsCount: undefined,
          originalState: undefined
        }));

        // 保存到localStorage
        saveToLocalStorage(cleanRows);
      } else {
        console.warn('确认合并：没有可用的原始SRT数据');
      }

      // 如果有合并行，记录日志
      if (hasMergedRows) {
        const mergedCount = resultRows.filter(row => row.isMerged).length;
        const totalCount = resultRows.length;
        console.log(`确认合并：共 ${totalCount} 行，其中 ${mergedCount} 行是合并行`);
      }

      // 发送更新事件
      emit('update-rows', resultRows);
      emit('update:show', false);

      console.log('确认合并操作完成');
    }

    return {
      // 状态
      subtitleItems,
      mergedGroups,
      hasChanges,
      showAIGrouping,

      // 验证错误弹窗状态
      showValidationError,
      validationErrorType,
      validationErrorTitle,
      validationErrorMessage,
      validationErrorDetails,

      // 方法
      handleClick,
      handleRightClick,
      cancelAllMergedGroups,
      exportText,
      importGroups,
      applyAIGrouping,
      onConfirmMerge
    };
  }
}
</script>

<style scoped>
.adjust-shots-drawer {
  background: #232136;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 1rem 1.2rem 0.5rem 1.2rem; /* 减少顶部和底部padding */
}

.drawer-header-row {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.drawer-title {
  color: #e0def4;
  font-size: 1.25rem; /* 稍微小一点 */
  font-weight: 600;
  margin-bottom: 0.1rem;
}

.drawer-description {
  color: #908caa;
  font-size: 0.98rem;
  margin-bottom: 0.2rem;
  line-height: 1.4;
}

.action-buttons {
  display: flex;
  gap: 0.7rem;
  margin-bottom: 0.2rem;
  flex-wrap: wrap;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  padding: 0.5rem 0.8rem;
  background: #2a273f;
  border: 1px solid #44415a;
  color: #e0def4;
  border-radius: 5px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background 0.2s;
}

.action-button:hover {
  background: #393552;
}

.action-button i {
  font-size: 1.1rem;
}

.ai-button {
  background: #3e8fb0;
  border-color: #3e8fb0;
}

.ai-button:hover {
  background: #2c6a85;
}

.status-bar {
  margin-bottom: 0.2rem;
  font-size: 0.92rem;
}

.status-text {
  color: #908caa;
}

.subtitle-list {
  flex: 1 1 0;
  min-height: 0;
  margin-bottom: 0.5rem;
  display: flex;
  flex-direction: column;
  border: 1px solid #44415a;
  border-radius: 5px;
  overflow: hidden;
}

.subtitle-list-header {
  background: #2a273f;
  border-bottom: 1px solid #44415a;
  padding: 0.7rem 1rem;
}

.column-header {
  color: #c4a7e7;
  font-weight: 600;
  font-size: 0.95rem;
}

.subtitle-items-container {
  flex: 1;
  overflow-y: auto;
  background: #1f1d2e;
}

.subtitle-items {
  display: flex;
  flex-direction: column;
}

.subtitle-item {
  display: flex;
  align-items: flex-start;
  padding: 0.7rem 1rem;
  border-bottom: 1px solid #2a273f;
  cursor: pointer;
  transition: background 0.2s;
}

.subtitle-item:hover {
  background: #2a273f;
}

.subtitle-item.merged {
  background: #3e8fb0;
  color: #e0def4;
}

.subtitle-item.merged:hover {
  background: #2c6a85;
}

.item-serial {
  color: #908caa;
  font-size: 0.8rem;
  width: 3rem;
  flex-shrink: 0;
  padding-top: 0.1rem;
}

.item-content {
  flex: 1;
  color: #e0def4;
  line-height: 1.4;
  word-break: break-word;
  white-space: pre-line;
}

.drawer-footer {
  padding-top: 0.7rem;
  border-top: 1px solid #44415a;
  margin-top: 0.5rem;
}

.operation-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.operation-button {
  padding: 0.6rem 2rem;
  border-radius: 6px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;
}

.operation-button.cancel {
  background: transparent;
  border: 1px solid #908caa;
  color: #908caa;
}

.operation-button.cancel:hover {
  background: #2a273f;
}

.operation-button.confirm {
  background: #3e8fb0;
  border: none;
  color: #e0def4;
}

.operation-button.confirm:hover {
  background: #2c6a85;
}
</style>









