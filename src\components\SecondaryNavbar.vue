<template>
  <div class="secondary-navbar">
    <div class="logo-section">
      绘图: {{ drawingCount }}/{{ totalDrawings }}
    </div>
    <button 
      v-for="tab in tabs" 
      :key="tab.id"
      :class="['tab-button', { active: activeTab === tab.id }]"
      @click="changeTab(tab.id)"
    >
      {{ tab.name }}
    </button>
    
    <div class="right-actions">
      <button 
        v-for="action in actions" 
        :key="action.id"
        class="action-btn"
        @click="handleAction(action.id)"
      >
        {{ action.name }}
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SecondaryNavbar',
  props: {
    drawingCount: {
      type: Number,
      default: 0
    },
    totalDrawings: {
      type: Number,
      default: 129
    },
    activeTab: {
      type: String,
      default: 'match-tags'
    }
  },
  data() {
    return {
      tabs: [
        { id: 'adjust-shots', name: '调整分镜' },
        { id: 'match-tags', name: '全局推理' },
        { id: 'manage-tags', name: '管理标签' },
        { id: 'generate-intro', name: '待定' }
      ],
      actions: [
        { id: 'smart-reasoning', name: '1. 智能推理' },
        { id: 'generate-images', name: '2. 生成配图' },
        { id: 'enlarge-images', name: '3. 放大配图' }
      ]
    }
  },
  methods: {
    changeTab(tabId) {
      this.$emit('tab-change', tabId);
    },
    handleAction(actionId) {
      this.$emit('action', actionId);
    }
  }
}
</script>

<style scoped>
/* 次级导航栏样式 */
.secondary-navbar {
  display: flex;
  align-items: center;
  background-color: #1a1a1a;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #333;
}

.secondary-navbar .logo-section {
  font-size: 0.9rem;
  color: #cccccc;
  margin-right: 1rem;
}

.tab-button {
  background-color: #222;
  border: 1px solid #333;
  color: #aaa;
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  position: relative;
  margin-right: 0.5rem;
  transition: all 0.2s ease;
}

.tab-button.active {
  background-color: #222;
  border: 1px solid #333;
  color: #aaa;
}

.right-actions {
  margin-left: auto;
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  background-color: #222;
  border: 1px solid #333;
  color: #aaa;
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.2s ease;
}

.action-btn:hover, .tab-button:hover {
  background-color: #2d2d2d;
  border-color: #444;
  color: #e0e0e0;
}
</style> 