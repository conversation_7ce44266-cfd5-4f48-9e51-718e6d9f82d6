const express = require('express');
const fs = require('fs');
const path = require('path');
const multer = require('multer');
const fsPromises = require('fs').promises;
const fetch = require('node-fetch');
const bodyParser = require('body-parser');

const router = express.Router();

// 创建本地文件夹
router.post('/create-folder', (req, res) => {
  console.log('收到创建文件夹请求:', req.body);
  const { folderName } = req.body;

  // 检查请求体
  if (!folderName) {
    console.log('错误: 缺少文件夹名称');
    return res.status(400).json({ error: '缺少文件夹名称' });
  }

  // 只允许单层文件夹名，防止路径穿越
  if (folderName.includes('/') || folderName.includes('\\')) {
    console.log('错误: 文件夹名称包含非法字符:', folderName);
    return res.status(400).json({ error: '文件夹名称不能包含路径分隔符' });
  }

  // 获取base参数，如果有将其与基础目录结合
  const { base = '' } = req.body;

  const baseDir = path.resolve(__dirname, '../../draft');
  // 生成完整的目标路径（包含 base）
  const targetBaseDir = base ? path.join(baseDir, base) : baseDir;
  const folderPath = path.join(targetBaseDir, folderName);

  console.log('基础目录:', baseDir);
  console.log('目标目录（含 base）:', targetBaseDir);
  console.log('最终文件夹路径:', folderPath);

  // 确保基础目录和目标基础目录存在
  if (!fs.existsSync(baseDir)) {
    console.log('基础目录不存在，正在创建:', baseDir);
    fs.mkdirSync(baseDir, { recursive: true });
    console.log('基础目录创建成功');
  }

  // 确保 targetBaseDir 存在（可能是多级目录）
  if (base && !fs.existsSync(targetBaseDir)) {
    console.log('目标基础目录不存在，正在创建:', targetBaseDir);
    fs.mkdirSync(targetBaseDir, { recursive: true });
    console.log('目标基础目录创建成功');
  }

  // 检查目标文件夹是否已存在
  if (fs.existsSync(folderPath)) {
    console.log('文件夹已存在:', folderPath);
    return res.status(409).json({ error: '文件夹已存在' });
  }

  console.log('正在创建文件夹:', folderPath);
  try {
    fs.mkdirSync(folderPath, { recursive: true });
    console.log('文件夹创建成功');
    res.json({ success: true, folderPath });
  } catch (err) {
    console.error('创建文件夹失败:', err);
    res.status(500).json({ error: err.message });
  }
});

// 获取所有项目文件夹，支持 base 参数
router.get('/list-folders', (req, res) => {
  console.log('收到获取文件夹列表请求:', req.query);
  const base = req.query.base || '';
  const baseDir = path.resolve(__dirname, '../../draft', base.replace(/^draft\/?/, ''));
  console.log('查询目录:', baseDir);

  if (!fs.existsSync(baseDir)) {
    console.log('目录不存在，返回空数组');
    return res.json([]);
  }

  try {
    const allFiles = fs.readdirSync(baseDir);
    console.log('目录下所有文件/文件夹:', allFiles);

    const folders = allFiles.filter(name => {
      try {
        return fs.statSync(path.join(baseDir, name)).isDirectory();
      } catch (err) {
        console.error(`检查文件夹状态失败: ${name}`, err);
        return false;
      }
    });

    console.log('筛选后的文件夹:', folders);
    res.json(folders);
  } catch (err) {
    console.error('获取文件夹列表失败:', err);
    res.status(500).json({ error: err.message });
  }
});

// 新增：重命名文件夹
router.post('/rename-folder', (req, res) => {
  const { oldName, newName, base } = req.body;
  console.log('重命名文件夹请求:', { oldName, newName, base });

  if (!oldName || !newName) {
    return res.status(400).json({ error: '缺少参数' });
  }
  if (oldName === newName) {
    return res.json({ success: true });
  }

  const baseDir = path.resolve(__dirname, '../../draft');
  console.log('基础目录路径:', baseDir);

  // 非常关键 - 处理多种路径情况
  let oldPath, newPath;

  // 先检查路径中是否包含斜杠，如果包含说明是复合路径
  if (oldName.includes('/') || oldName.includes('\\')) {
    // 路径直接包含斜杠，需要拆分处理
    console.log(`检测到复合路径: ${oldName}`);
    const parts = oldName.split(/[\/\\]/);
    const actualFileName = parts.pop();
    const parentDir = parts.join('/');
    oldPath = path.join(baseDir, parentDir, actualFileName);
    newPath = path.join(baseDir, parentDir, newName);
    console.log(`处理复合路径结果: 父目录=${parentDir}, 文件名=${actualFileName}`);
  } else if (base) {
    // 含有项目目录（base），处理章节重命名
    console.log(`处理章节重命名: 项目=${base}, 旧章节=${oldName}, 新章节=${newName}`);
    oldPath = path.join(baseDir, base, oldName);
    newPath = path.join(baseDir, base, newName);
  } else {
    // 默认情况：直接在 draft 目录下重命名
    console.log(`直接重命名项目目录: 旧项目=${oldName}, 新项目=${newName}`);
    oldPath = path.join(baseDir, oldName);
    newPath = path.join(baseDir, newName);
  }

  try {
    // 检查路径存在性 - 增强日志
    console.log('重命名路径检查:', {
      oldPath,
      newPath,
      原请求参数: { oldName, newName, base },
      绝对路径检查: {
        baseDir,
        oldPathExists: fs.existsSync(oldPath),
        baseExists: base ? fs.existsSync(path.join(baseDir, base)) : '无base参数'
      }
    });

    // 列出目录内容以确认
    if (base) {
      const basePath = path.join(baseDir, base);
      if (fs.existsSync(basePath)) {
        console.log(`项目目录 "${base}" 存在，内容:`, fs.readdirSync(basePath));
      } else {
        console.error(`项目目录 "${base}" 不存在!`);
      }
    }

    if (!fs.existsSync(oldPath)) {
      console.error('原路径不存在:', oldPath);
      return res.status(404).json({
        error: '原文件夹不存在',
        details: { oldPath, base, requestParams: req.body }
      });
    }
    if (fs.existsSync(newPath)) {
      console.error('目标路径已存在:', newPath);
      return res.status(409).json({ error: '新文件夹已存在' });
    }

    // 执行重命名
    console.log('执行重命名:', oldPath, '->', newPath);
    fs.renameSync(oldPath, newPath);
    res.json({ success: true });
  } catch (err) {
    console.error('重命名发生错误:', err);
    res.status(500).json({ error: err.message });
  }
});

// 新增：删除文件夹
router.post('/delete-folder', (req, res) => {
  const { folderName } = req.body;
  if (!folderName) {
    return res.status(400).json({ error: '缺少参数' });
  }
  if (folderName.includes('/') || folderName.includes('\\')) {
    return res.status(400).json({ error: '文件夹名称不能包含路径分隔符' });
  }

  // 获取 base 参数，支持多级目录
  const { base = '' } = req.body;

  const baseDir = path.resolve(__dirname, '../../draft');
  // 生成完整的目标路径（包含 base）
  const targetBaseDir = base ? path.join(baseDir, base) : baseDir;

  // 确保目标目录存在
  if (!fs.existsSync(targetBaseDir)) {
    return res.status(404).json({ error: '目标目录不存在' });
  }

  const folderPath = path.join(targetBaseDir, folderName);
  try {
    if (!fs.existsSync(folderPath)) {
      return res.status(404).json({ error: '文件夹不存在' });
    }
    // 递归删除文件夹
    const rimraf = (dir) => {
      if (fs.existsSync(dir)) {
        fs.readdirSync(dir).forEach((file) => {
          const curPath = path.join(dir, file);
          if (fs.lstatSync(curPath).isDirectory()) {
            rimraf(curPath);
          } else {
            fs.unlinkSync(curPath);
          }
        });
        fs.rmdirSync(dir);
      }
    };
    rimraf(folderPath);
    res.json({ success: true });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});

// 新端点: 专门处理章节重命名
router.post('/rename-chapter', (req, res) => {
  const { oldName, newName, projectName } = req.body;
  console.log('章节重命名请求:', { oldName, newName, projectName });

  if (!oldName || !newName || !projectName) {
    return res.status(400).json({ error: '缺少参数' });
  }
  if (oldName === newName) {
    return res.json({ success: true });
  }

  // 章节重命名的关键是路径拼接正确
  const baseDir = path.resolve(__dirname, '../../draft');
  const oldPath = path.join(baseDir, projectName, oldName);
  const newPath = path.join(baseDir, projectName, newName);

  console.log('章节重命名路径:', {
    baseDir,
    projectName,
    oldName,
    newName,
    oldPath,
    newPath,
    oldPathExists: fs.existsSync(oldPath)
  });

  try {
    if (!fs.existsSync(oldPath)) {
      console.error('原章节路径不存在:', oldPath);
      return res.status(404).json({ error: '原章节文件夹不存在' });
    }
    if (fs.existsSync(newPath)) {
      console.error('目标章节路径已存在:', newPath);
      return res.status(409).json({ error: '新章节文件夹已存在' });
    }

    // 执行重命名
    fs.renameSync(oldPath, newPath);
    console.log('章节重命名成功:', oldPath, ' -> ', newPath);
    res.json({ success: true });
  } catch (err) {
    console.error('章节重命名出错:', err);
    res.status(500).json({ error: err.message });
  }
});

// 新增：保存项目特定的JSON文件 (例如 Global.json)
router.post('/save-project-file', async (req, res) => {
  const { projectTitle, fileName, content, filePath, chapterTitle } = req.body;
  console.log('保存项目文件请求:', { projectTitle, fileName, filePath, chapterTitle });

  if (!fileName || content === undefined) {
    return res.status(400).json({ success: false, error: '缺少 fileName 或 content 参数' });
  }

  // 验证 fileName
  if (!fileName.endsWith('.json') || fileName.includes('/') || fileName.includes('\\')) {
    return res.status(400).json({ success: false, error: '无效的文件名或类型。只允许 .json 文件，且文件名不能包含路径分隔符。' });
  }

  try {
    let targetPath;
    if (filePath) {
      // 如果前端传了完整路径，直接用
      targetPath = path.resolve(__dirname, '../../', filePath);
    } else if (projectTitle && chapterTitle) {
      // 如果有章节名，拼接章节目录
      targetPath = path.resolve(__dirname, '../../draft', projectTitle, chapterTitle, fileName);
    } else if (projectTitle) {
      // 兼容老逻辑
      targetPath = path.resolve(__dirname, '../../draft', projectTitle, fileName);
    } else {
      return res.status(400).json({ success: false, error: '缺少 projectTitle 或 filePath' });
    }

    // 确保目录存在
    await fs.promises.mkdir(path.dirname(targetPath), { recursive: true });

    // 写入文件
    await fs.promises.writeFile(targetPath, JSON.stringify(content, null, 2), 'utf8');
    console.log(`文件成功保存到: ${targetPath}`);
    res.json({ success: true, message: '文件保存成功', path: targetPath });
  } catch (error) {
    console.error(`保存文件 ${fileName} 失败:`, error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 添加文件上传处理 - 用于上传字幕和音频文件

// 处理文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    console.log('====== STORAGE DESTINATION 调试 ======');
    console.log('请求体原始内容:', req.body);
    console.log('Multer 文件信息:', file);
    
    // 从URL查询参数中获取项目和章节信息（作为备选）
    const urlParams = new URL(`http://example.com${req.url}`).searchParams;
    const projectFromUrl = urlParams.get('projectTitle');
    const chapterFromUrl = urlParams.get('chapterTitle');
    
    // 优先使用请求体中的数据，其次使用URL参数
    const projectTitle = req.body.projectTitle || projectFromUrl;
    const chapterTitle = req.body.chapterTitle || chapterFromUrl;
    
    console.log('参数解析结果:', { 
      projectTitle, 
      chapterTitle, 
      fromBody: !!req.body.projectTitle, 
      fromUrl: !!projectFromUrl
    });

    if (!projectTitle || !chapterTitle) {
      console.error('项目标题或章节标题缺失 (destination):', { projectTitle, chapterTitle });
      return cb(new Error('项目标题或章节标题缺失'));
    }

    // 构建保存目录
    const targetDir = path.resolve(__dirname, '../../draft', projectTitle, chapterTitle);
    console.log('目标保存目录:', targetDir);

    // 确保目录存在
    try {
      if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir, { recursive: true });
        console.log('创建目录:', targetDir);
      }
      cb(null, targetDir);
    } catch (error) {
      console.error('创建目录失败:', error);
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    // 使用原始文件名
    console.log('设置文件名:', file.originalname);
    cb(null, file.originalname);
  }
});

// 处理文件上传
router.post('/upload-file', (req, res) => {
  console.log('接收到文件上传请求');
  console.log('请求体预览:', req.body); // 这可能是空的，因为multer还没解析
  
  // 使用单独的multer实例而不是共享实例
  const singleUpload = multer({ 
    storage,
    limits: {
      fileSize: 50 * 1024 * 1024, // 限制50MB
    },
    fileFilter: (req, file, cb) => {
      // SRT和音频文件类型过滤
      console.log('上传文件类型:', file.mimetype, '文件名:', file.originalname);
      if (file.originalname.toLowerCase().endsWith('.srt') || 
          file.mimetype.startsWith('audio/')) {
        cb(null, true);
      } else {
        cb(new Error('仅支持SRT和音频文件'));
      }
    }
  }).single('file');

  singleUpload(req, res, (err) => {
    console.log('Multer处理完成，检查结果');
    console.log('请求体内容 (multer处理后):', req.body);
    
    if (err) {
      console.error('文件上传处理错误:', err);
      return res.status(400).json({ 
        success: false, 
        error: err.message 
      });
    }

    if (!req.file) {
      console.error('没有文件被上传');
      return res.status(400).json({ 
        success: false, 
        error: '没有文件被上传' 
      });
    }

    const projectTitle = req.body.projectTitle;
    const chapterTitle = req.body.chapterTitle;
    
    console.log('文件上传参数检查:', { 
      projectTitle, 
      chapterTitle,
      'req.body类型': typeof req.body,
      '请求体内容': req.body,
      'req.file': req.file ? '存在' : '不存在'
    });

    if (!projectTitle || !chapterTitle) {
      console.error('项目标题或章节标题缺失:', { projectTitle, chapterTitle });
      return res.status(400).json({ 
        success: false, 
        error: '项目标题或章节标题缺失',
        debug: { 
          body: req.body,
          hasFile: !!req.file,
          contentType: req.headers['content-type']
        }
      });
    }

    console.log('文件上传成功:', { 
      projectTitle, 
      chapterTitle, 
      fileName: req.file.originalname,
      fileSize: req.file.size
    });

    // 返回文件保存路径
    const relativePath = path.join('draft', projectTitle, chapterTitle, req.file.originalname)
      .replace(/\\/g, '/'); // 替换Windows路径分隔符为Web分隔符

    console.log('返回相对路径:', relativePath);
    
    res.json({
      success: true,
      path: relativePath,
      filename: req.file.originalname
    });
  });
});

// 检查是否已经有list-files接口，如果没有则添加
// 列出目录中的文件（只列出文件，不包括子目录）
router.get('/list-files', (req, res) => {
  const targetPath = req.query.path || '';
  console.log('列出目录文件请求:', targetPath);

  // 绝对路径转换
  const fullPath = path.resolve(__dirname, '../../', targetPath);
  console.log('完整路径:', fullPath);

  try {
    if (!fs.existsSync(fullPath)) {
      console.log('目录不存在, 创建目录:', fullPath);
      fs.mkdirSync(fullPath, { recursive: true });
      return res.json([]);
    }

    // 获取目录内容
    const files = fs.readdirSync(fullPath).filter(item => {
      const itemPath = path.join(fullPath, item);
      // 只返回文件，不返回目录
      return fs.statSync(itemPath).isFile();
    });

    console.log('目录内文件:', files);
    res.json(files);
  } catch (error) {
    console.error('列出文件错误:', error);
    res.status(500).json({ error: error.message });
  }
});

// 删除文件
router.post('/delete-file', (req, res) => {
  const { filePath } = req.body;

  if (!filePath) {
    return res.status(400).json({ error: '缺少文件路径' });
  }

  console.log('删除文件请求:', filePath);

  // 绝对路径转换
  const fullPath = path.resolve(__dirname, '../../', filePath);
  console.log('完整删除路径:', fullPath);

  try {
    if (!fs.existsSync(fullPath)) {
      return res.status(404).json({ error: '文件不存在' });
    }

    // 确认是文件而不是目录
    if (!fs.statSync(fullPath).isFile()) {
      return res.status(400).json({ error: '目标不是文件' });
    }

    // 删除文件
    fs.unlinkSync(fullPath);
    console.log('文件删除成功:', fullPath);

    res.json({ success: true, message: '文件删除成功' });
  } catch (error) {
    console.error('删除文件错误:', error);
    res.status(500).json({ error: error.message });
  }
});

// 读取文件内容
router.get('/read-file', (req, res) => {
  const { path: filePath } = req.query;

  if (!filePath) {
    return res.status(400).json({
      success: false,
      error: '缺少文件路径参数'
    });
  }

  console.log('读取文件内容请求:', filePath);

  // 绝对路径转换
  const fullPath = path.resolve(__dirname, '../../', filePath);
  console.log('完整文件路径:', fullPath);

  try {
    if (!fs.existsSync(fullPath)) {
      return res.status(404).json({
        success: false,
        error: '文件不存在',
        path: fullPath
      });
    }

    // 确认是文件而不是目录
    if (!fs.statSync(fullPath).isFile()) {
      return res.status(400).json({
        success: false,
        error: '目标不是文件'
      });
    }

    // 读取文件内容
    const content = fs.readFileSync(fullPath, 'utf8');
    console.log('文件读取成功，内容长度:', content.length);

    res.json({
      success: true,
      content: content,
      path: filePath
    });
  } catch (error) {
    console.error('读取文件错误:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 提供文件内容的替代端点 (用于兼容性)
router.get('/file-content', (req, res) => {
  const { path: filePath } = req.query;

  if (!filePath) {
    return res.status(400).json({
      success: false,
      error: '缺少文件路径参数'
    });
  }

  console.log('获取文件内容请求 (兼容端点):', filePath);

  // 绝对路径转换
  const fullPath = path.resolve(__dirname, '../../', filePath);
  console.log('完整文件路径:', fullPath);

  try {
    if (!fs.existsSync(fullPath)) {
      return res.status(404).json({
        success: false,
        error: '文件不存在'
      });
    }

    // 确认是文件而不是目录
    if (!fs.statSync(fullPath).isFile()) {
      return res.status(400).json({
        success: false,
        error: '目标不是文件'
      });
    }

    // 读取文件内容
    const content = fs.readFileSync(fullPath, 'utf8');
    console.log('文件读取成功，内容长度:', content.length);

    res.json({
      success: true,
      content: content,
      path: filePath
    });
  } catch (error) {
    console.error('读取文件错误:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 保存项目数据
router.post('/save-project-data', (req, res) => {
  const { projectTitle, chapterTitle, data } = req.body;

  if (!projectTitle || !chapterTitle) {
    return res.status(400).json({ error: '缺少项目名称或章节名称' });
  }

  console.log('保存项目数据请求:', { projectTitle, chapterTitle });

  // 构建数据文件路径
  const targetDir = path.resolve(__dirname, '../../draft', projectTitle, chapterTitle);
  const dataFilePath = path.join(targetDir, 'project-data.json');

  try {
    // 确保目录存在
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
    }

    // 检查是否有合并行
    if (data && data.rows) {
      const mergedRows = data.rows.filter(row => row.isMerged);
      if (mergedRows.length > 0) {
        console.log(`项目包含 ${mergedRows.length} 个合并行`);
      }
    }

    // 写入数据文件
    fs.writeFileSync(dataFilePath, JSON.stringify(data, null, 2));
    console.log('项目数据保存成功:', dataFilePath);

    res.json({ success: true, message: '项目数据保存成功' });
  } catch (error) {
    console.error('保存项目数据错误:', error);
    res.status(500).json({ error: error.message });
  }
});

// 确保设置目录存在
async function ensureSettingsDir() {
  const settingsDir = path.join(process.cwd(), 'userdata');
  try {
    await fsPromises.access(settingsDir);
  } catch {
    await fsPromises.mkdir(settingsDir, { recursive: true });
  }
  return settingsDir;
}

// 保存用户设置 - 增强版
router.post('/save-user-settings', async (req, res) => {
  console.log('===== 接收到保存设置请求 =====');
  console.log('请求头:', req.headers);
  console.log('请求体类型:', typeof req.body);
  console.log('请求体内容:', req.body);

  // 检查请求体是否为空
  if (!req.body || Object.keys(req.body).length === 0) {
    console.error('请求体为空或未正确解析');
    return res.status(400).json({
      success: false,
      error: '请求体为空或未正确解析'
    });
  }

  try {
    // 直接从 req.body 中获取参数，避免解构失败
    const settings = req.body.settings;
    const filename = req.body.filename;

    console.log('提取的参数:', { settings: settings ? '存在' : '不存在', filename });

    if (!settings) {
      console.error('缺少 settings 参数');
      return res.status(400).json({ success: false, error: '缺少 settings 参数' });
    }

    if (!filename) {
      console.error('缺少 filename 参数');
      return res.status(400).json({ success: false, error: '缺少 filename 参数' });
    }

    console.log('开始保存文件...');
    const settingsDir = await ensureSettingsDir();
    const filePath = path.join(settingsDir, filename);

    // 确保 settings 是字符串
    const settingsStr = typeof settings === 'string' ? settings : JSON.stringify(settings, null, 2);
    console.log('处理后的字符串长度:', settingsStr.length);

    await fsPromises.writeFile(filePath, settingsStr);
    console.log('文件写入成功:', filePath);

    res.json({
      success: true,
      path: filePath
    });
  } catch (error) {
    console.error('保存用户设置异常:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 加载用户设置
router.get('/load-user-settings', async (req, res) => {
  try {
    const { filename } = req.query;
    if (!filename) {
      return res.status(400).json({ success: false, error: '缺少文件名参数' });
    }

    const settingsDir = await ensureSettingsDir();
    const filePath = path.join(settingsDir, filename);

    try {
      const settings = await fsPromises.readFile(filePath, 'utf8');
      res.json({
        success: true,
        settings: JSON.parse(settings),
        path: filePath
      });
    } catch (error) {
      if (error.code === 'ENOENT') {
        // 文件不存在时返回默认设置
        const defaultSettings = {
          theme: 'dark',
          language: 'zh-CN',
          autoSave: true,
          llm: {
            provider: 'openrouter',
            defaultModel: 'anthropic/claude-3-sonnet',
            temperature: 0.7,
            localUrl: 'http://localhost:8080'
          }
        };

        // 自动创建默认设置文件
        await fsPromises.writeFile(filePath, JSON.stringify(defaultSettings, null, 2));

        res.json({
          success: true,
          settings: defaultSettings,
          path: filePath
        });
      } else {
        throw error;
      }
    }
  } catch (error) {
    console.error('加载用户设置出错:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 检查文件是否存在
router.get('/check-file-exists', (req, res) => {
  const { filePath } = req.query;
  console.log('检查文件是否存在:', filePath);
  
  if (!filePath) {
    return res.status(400).json({ error: '缺少文件路径参数' });
  }

  try {
    // 规范化路径，确保安全
    const fullPath = path.resolve(__dirname, '../../', filePath);
    // 确保文件路径在允许的目录内，避免目录遍历攻击
    const baseDir = path.resolve(__dirname, '../../');
    if (!fullPath.startsWith(baseDir)) {
      return res.status(403).json({ error: '访问路径不允许' });
    }
    
    const exists = fs.existsSync(fullPath);
    console.log('文件路径:', fullPath, '存在状态:', exists);
    
    res.json({ exists });
  } catch (error) {
    console.error('检查文件存在性出错:', error);
    res.status(500).json({ error: error.message });
  }
});

// 保存文本文件
router.post('/save-text-file', async (req, res) => {
  const { filePath, content } = req.body;
  
  if (!filePath || content === undefined) {
    return res.status(400).json({ success: false, error: '缺少文件路径或内容' });
  }
  
  console.log('保存文本文件请求:', { filePath, contentLength: content.length });
  
  // 绝对路径转换
  const fullPath = path.resolve(__dirname, '../../', filePath);
  console.log('完整文件路径:', fullPath);
  
  try {
    // 确保目录存在
    const dirPath = path.dirname(fullPath);
    if (!fs.existsSync(dirPath)) {
      console.log('目录不存在，创建目录:', dirPath);
      fs.mkdirSync(dirPath, { recursive: true });
    }
    
    // 写入文件
    await fsPromises.writeFile(fullPath, content, 'utf8');
    console.log('文件保存成功:', fullPath);
    
    res.json({ success: true, message: '文件保存成功', path: filePath });
  } catch (error) {
    console.error('保存文件错误:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 辅助函数：加载用户LLM设置
async function loadLLMSettings() {
  const settingsPath = path.join(process.cwd(), 'userdata', 'usersettings.json');
  try {
    const data = await fsPromises.readFile(settingsPath, 'utf8');
    const settings = JSON.parse(data);
    return settings.llm;
  } catch (error) {
    console.error('无法加载LLM设置:', error);
    // 返回一个安全的默认值或抛出错误
    return { provider: 'none' }; // 表示无有效配置
  }
}

// 辅助函数：调用LLM API (简化示例)
async function callLLMAPI(prompt, settings) {
  let apiUrl, headers, requestBody;
  const model = settings.defaultModel || ''; // 获取模型设置
  const apiKey = settings[`${settings.provider}ApiKey`] || ''; // 获取对应提供商的API Key
  const temperature = settings.temperature || 0.7;

  console.log(`[LLM Backend] 使用提供商: ${settings.provider}, 模型: ${model}`);

  switch (settings.provider) {
    case 'google':
      if (!apiKey) throw new Error('缺少 Google API Key');
      apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${apiKey}`;
      headers = { 'Content-Type': 'application/json' };
      requestBody = {
        contents: [{ parts: [{ text: prompt }] }],
        generationConfig: { temperature }
      };
      break;

    case 'openrouter': {
      if (!apiKey) throw new Error('缺少 OpenRouter API Key');
      apiUrl = 'https://openrouter.ai/api/v1/chat/completions';
      // OpenRouter 需要特定的模型格式 provider/model
      let openRouterModel = model.includes('/') ? model : `google/${model}`; // 尝试修正格式
      console.log(`[LLM Backend] OpenRouter 使用模型: ${openRouterModel}`);
      headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        // OpenRouter 可能需要这些头信息
        // 'HTTP-Referer': 'YOUR_SITE_URL',
        // 'X-Title': 'YOUR_APP_NAME'
      };
      requestBody = {
        model: openRouterModel,
        messages: [{ role: 'user', content: prompt }],
        temperature
      };
      break;
    }

    case 'openai':
      if (!apiKey) throw new Error('缺少 OpenAI API Key');
      apiUrl = 'https://api.openai.com/v1/chat/completions';
      headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      };
      requestBody = {
        model: model || 'gpt-3.5-turbo', // 默认模型
        messages: [{ role: 'user', content: prompt }],
        temperature
      };
      break;

    case 'anthropic':
       if (!apiKey) throw new Error('缺少 Anthropic API Key');
       apiUrl = 'https://api.anthropic.com/v1/messages';
       headers = {
         'Content-Type': 'application/json',
         'Authorization': `Bearer ${apiKey}`,
         'anthropic-version': '2023-06-01' // 必需
       };
       requestBody = {
         model: model || 'claude-3-haiku-20240307', // 默认模型
         max_tokens: 4000, // Anthropic 通常需要指定 max_tokens
         messages: [{ role: 'user', content: prompt }],
         temperature
       };
       break;

    case 'local':
       // 处理本地LLM，需要正确的 localUrl (通常包含完整路径)
       apiUrl = settings.localUrl; // 假设localUrl是完整的API端点
       if (!apiUrl) throw new Error('缺少本地 LLM URL (localUrl)');
       console.log(`[LLM Backend] 调用本地LLM: ${apiUrl}`);
       headers = { 'Content-Type': 'application/json' };
       // 本地LLM的请求体格式可能不同，这里使用类似OpenAI的格式作为示例
       requestBody = {
         model: model || 'local-model', // 可能需要指定模型
         messages: [{ role: 'user', content: prompt }],
         temperature,
         // 可能需要其他参数，如 stream: false
       };
       // 如果本地API需要密钥
       if (settings.localApiKey) {
         headers['Authorization'] = `Bearer ${settings.localApiKey}`;
       }
       break;

    default:
      throw new Error(`不支持的LLM提供商: ${settings.provider}`);
  }

  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[LLM Backend] ${settings.provider} API 请求失败: ${response.status}`, errorText);
      throw new Error(`${settings.provider} API 请求失败: ${response.status}. 响应: ${errorText}`);
    }

    const responseData = await response.json();
    console.log(`[LLM Backend] ${settings.provider} API 响应成功`);

    // 提取文本 (需要根据不同提供商调整)
    let extractedText = '';
    if (settings.provider === 'google') {
       extractedText = responseData.candidates?.[0]?.content?.parts?.[0]?.text || '';
    } else if (settings.provider === 'anthropic') {
       extractedText = responseData.content?.[0]?.text || '';
    } else { // OpenAI, OpenRouter, Local (假设类似OpenAI)
       extractedText = responseData.choices?.[0]?.message?.content || '';
    }

    if (!extractedText) {
        console.warn('[LLM Backend] 未能从API响应中提取文本:', responseData);
        // 可以尝试返回原始响应，让前端处理
        // throw new Error('未能从API响应中提取文本');
        return JSON.stringify(responseData); // 返回原始JSON字符串
    }
    
    return extractedText;

  } catch (error) {
    console.error(`[LLM Backend] 调用 ${settings.provider} API 时出错:`, error);
    throw error; // 将错误向上抛出
  }
}

// 辅助函数：为特定任务构建提示词
function buildPrompt(text, analyzeType) {
  switch (analyzeType) {
    case 'global-reasoning':
      return `
任务：分析剧本/故事文本，提取关键视觉元素。

请仔细阅读以下文本内容：
---
${text}
---

根据文本，提取以下信息，并以严格的JSON格式返回。请确保JSON结构完整且所有字符串值都使用双引号包裹。

1.  **角色列表 (characters)**: 数组形式。每个角色对象应包含：
    *   \`name\` (string): 角色名称。
    *   \`visualTraits\` (string): 角色的关键视觉描述（服装、外貌、武器、配饰等）。

2.  **时代/背景设定 (setting)**: 对象形式。应包含：
    *   \`name\` (string, optional): 时代或环境的名称（如"唐朝长安"、"赛博朋克都市"）。
    *   \`era\` (string, optional): 更具体的时代描述（如"盛唐时期"、"近未来"）。
    *   \`description\` (string): 对场景或背景的关键视觉描述。

3.  **武功/能力/特殊物品 (abilities)**: 数组形式。每个对象应包含：
    *   \`name\` (string): 武功、超能力或特殊物品的名称。
    *   \`visualEffect\` (string): 该能力或物品使用时的视觉效果描述。

如果某项信息在文本中未明确提及，请在对应字段留空字符串 "" 或省略该字段。
请确保输出的JSON是有效的。

输出示例 (仅为结构示例，内容需根据文本生成):
\`\`\`json
{
  "characters": [
    {
      "name": "李寻欢",
      "visualTraits": "身着白衣，手持飞刀，眼神忧郁"
    },
    {
      "name": "阿飞",
      "visualTraits": "快剑，沉默寡言，黑衣劲装"
    }
  ],
  "setting": {
    "name": "古龙江湖",
    "description": "风雪交加的小镇客栈，灯火昏黄"
  },
  "abilities": [
    {
      "name": "小李飞刀",
      "visualEffect": "一道银光闪过，快得无法看清"
    },
    {
      "name": "阿飞的快剑",
      "visualEffect": "剑光一闪，迅疾无比"
    }
  ]
}
\`\`\`

请直接输出JSON内容。
`;
    // 可以为其他 analyzeType 添加 case
    default:
      // 默认或通用提示词
      return `请分析以下文本：\n---\n${text}\n---`;
  }
}

// 新增：处理文本分析请求
router.post('/analyze-text', async (req, res) => {
  // 从请求体中解构 text, analyzeType, 以及我们期望的自定义 prompt
  const { text, analyzeType, prompt: customPromptFromRequest } = req.body;

  if (!text) { // analyzeType 可以暂时不作为硬性要求，如果提供了customPrompt
    return res.status(400).json({ success: false, error: '缺少 text 参数' });
  }

  // 如果没有自定义 prompt，但有 analyzeType，则按旧逻辑构建 prompt
  // 如果既没有自定义 prompt，也没有 analyzeType，则可能需要错误处理或默认行为
  if (!customPromptFromRequest && !analyzeType) {
    return res.status(400).json({ success: false, error: '缺少 analyzeType 或自定义 prompt 参数' });
  }

  console.log(`[API /analyze-text] 收到请求 - 类型: ${analyzeType || '自定义'}, 文本长度: ${text.length}`);
  if (customPromptFromRequest) {
    console.log(`[API /analyze-text] 收到自定义Prompt模板，长度: ${customPromptFromRequest.length}`);
  }

  try {
    // 1. 加载LLM设置
    const llmSettings = await loadLLMSettings();
    if (!llmSettings || llmSettings.provider === 'none') {
       return res.status(500).json({ success: false, error: '无法加载或未配置LLM设置' });
    }

    // 2. 构建最终的提示词给LLM
    let finalPromptForLLM;
    if (customPromptFromRequest) {
      // 如果前端提供了自定义prompt模板，使用它并替换占位符
      // 假设占位符是 [TEXT_PLACEHOLDER] 或 {text}
      finalPromptForLLM = customPromptFromRequest
                            .replace(/\[TEXT_PLACEHOLDER\]/g, text) // 兼容 [TEXT_PLACEHOLDER]
                            .replace(/\{text\}/g, text);          // 兼容 {text}
      console.log('[API /analyze-text] 使用前端提供的自定义Prompt模板并组合文本');
    } else {
      // 否则，使用旧的 buildPrompt 逻辑（基于 analyzeType）
      finalPromptForLLM = buildPrompt(text, analyzeType);
      console.log('[API /analyze-text] 使用后端内置的Prompt模板');
    }
    
    console.log(`[API /analyze-text] 最终发送给LLM的Prompt (前300字符): ${finalPromptForLLM.substring(0,300)}`);

    // 3. 调用LLM API
    console.log(`[API /analyze-text] 准备调用LLM: ${llmSettings.provider}`);
    const llmResponseText = await callLLMAPI(finalPromptForLLM, llmSettings); // 使用 finalPromptForLLM
    console.log(`[API /analyze-text] LLM 响应文本长度: ${llmResponseText.length}`);

    // 4. 处理响应 - 添加对'custom-prompt'的特殊处理
    let jsonData = null;
    let parseError = null;
    let rawOutput = llmResponseText; // 保留原始输出

    // 当analyzeType为'custom-prompt'时，不进行JSON解析，直接返回原始文本
    if (analyzeType === 'custom-prompt') {
      console.log('[API /analyze-text] 检测到custom-prompt类型，跳过JSON解析，直接返回原始文本');
      
      // 根据需求决定是否尝试解析为JSON - 这里我们不解析，直接返回原始文本
      res.json({
        success: true,
        data: null, // 不返回结构化数据，前端需要直接使用text
        text: rawOutput, // LLM返回的原始文本
        parseError: null
      });
      return;
    } else if (analyzeType === 'global-reasoning') {
      try {
        // 清理可能的Markdown代码块标记
        const cleanedText = llmResponseText
          .replace(/```json\n?/, '')
          .replace(/\n?```$/, '')
          .trim();
        jsonData = JSON.parse(cleanedText);
        console.log('[API /analyze-text] JSON 解析成功');
      } catch (err) {
        console.error('[API /analyze-text] JSON 解析失败:', err);
        parseError = err.message;
        // jsonData 保持为 null
      }
    }

    // 5. 返回结果
    res.json({
      success: true,
      data: jsonData, // 解析后的JSON数据，如果解析失败则为null
      text: rawOutput, // LLM返回的原始文本
      parseError: parseError // 如果JSON解析失败，包含错误信息
    });

  } catch (error) {
    console.error(`[API /analyze-text] 处理失败 - 类型: ${analyzeType || '自定义'}:`, error);
    res.status(500).json({
        success: false,
        error: `文本分析处理失败: ${error.message}`,
        provider: error.provider || undefined // 传递提供商信息（如果有）
     });
  }
});

// --- BEGIN Prompt Management API Endpoints ---
const USER_DATA_DIR = path.join(__dirname, '../../userData'); // Adjusted path assuming localCommunication.js is in src/server
const PROMPTS_JSON_PATH = path.join(process.cwd(), 'userdata', 'prompts.json');

// Helper function: 读取 prompts.json
async function readPromptsJson() {
  const data = await fsPromises.readFile(PROMPTS_JSON_PATH, 'utf8');
  return JSON.parse(data);
}
// Helper function: 写入 prompts.json
async function writePromptsJson(json) {
  await fsPromises.writeFile(PROMPTS_JSON_PATH, JSON.stringify(json, null, 2), 'utf8');
}

// 获取 globalReasoning.default
router.get('/prompt/default', async (req, res) => {
  try {
    const data = await readPromptsJson();
    if (data.globalReasoning && data.globalReasoning.default) {
      res.send(Array.isArray(data.globalReasoning.default)
        ? data.globalReasoning.default.join('\n')
        : data.globalReasoning.default);
    } else {
      res.status(404).send('prompts.json 缺少 globalReasoning.default');
    }
  } catch (error) {
    console.error('读取 prompts.json 失败:', error);
    res.status(500).send('读取 prompts.json 失败');
  }
});

// 获取 globalReasoning.user
router.get('/prompt/user', async (req, res) => {
  try {
    const data = await readPromptsJson();
    if (data.globalReasoning && data.globalReasoning.user) {
      res.send(Array.isArray(data.globalReasoning.user)
        ? data.globalReasoning.user.join('\n')
        : data.globalReasoning.user);
    } else {
      res.status(404).send('prompts.json 缺少 globalReasoning.user');
    }
  } catch (error) {
    console.error('读取 prompts.json 失败:', error);
    res.status(500).send('读取 prompts.json 失败');
  }
});

// 保存 globalReasoning.user
router.post('/prompt/user', bodyParser.json(), async (req, res) => {
  const { prompt } = req.body;
  if (typeof prompt !== 'string') {
    return res.status(400).send('Invalid prompt content: must be a string.');
  }
  try {
    const data = await readPromptsJson();
    if (!data.globalReasoning) data.globalReasoning = {};
    // 存为多行数组
    data.globalReasoning.user = prompt.split(/\r?\n/);
    await writePromptsJson(data);
    res.status(200).send('User prompt saved successfully.');
  } catch (error) {
    console.error('Error saving user prompt:', error);
    res.status(500).send('Error saving user prompt.');
  }
});

// --- END Prompt Management API Endpoints ---

// 新增：保存 prompts.json 的 aiGrouping.user 等内容
router.post('/save-userdata-prompts', async (req, res) => {
  try {
    const promptsPath = path.join(process.cwd(), 'userdata', 'prompts.json');
    const content = req.body;
    if (!content) {
      return res.status(400).json({ success: false, error: '缺少内容' });
    }
    await fs.promises.writeFile(promptsPath, JSON.stringify(content, null, 2), 'utf8');
    res.json({ success: true });
  } catch (e) {
    res.status(500).json({ success: false, error: e.message });
  }
});

module.exports = router;