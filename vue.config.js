const { defineConfig } = require('@vue/cli-service')
const http = require('http')
const express = require('express')
const path = require('path')

// 自动探测后端服务器端口函数
async function detectServerPort(startPort, maxPort) {
  // 首先尝试的端口顺序：8089, 然后是其他端口
  const ports = [8089]
  
  // 添加其他端口但排除已经在列表中的
  for (let port = startPort; port <= maxPort; port++) {
    if (!ports.includes(port)) {
      ports.push(port)
    }
  }

  console.log('尝试连接端口顺序:', ports.join(', '))

  // 尝试连接到每个端口
  for (const port of ports) {
    try {
      await new Promise((resolve, reject) => {
        console.log(`正在尝试连接端口 ${port}...`)
        const req = http.get(`http://localhost:${port}/api/health`, (res) => {
          if (res.statusCode === 200) {
            console.log(`端口 ${port} 连接成功，状态码: 200`)
            resolve(port)
          } else {
            console.log(`端口 ${port} 返回非成功状态码: ${res.statusCode}`)
            reject(new Error(`端口 ${port} 返回状态码 ${res.statusCode}`))
          }
        })
        
        req.on('error', (err) => {
          console.log(`连接到端口 ${port} 时出错: ${err.message}`)
          reject(new Error(`无法连接到端口 ${port}: ${err.message}`))
        })
        
        req.setTimeout(1000, () => {
          console.log(`连接到端口 ${port} 超时`)
          req.abort()
          reject(new Error(`连接到端口 ${port} 超时`))
        })
      })
      
      console.log(`在端口 ${port} 找到活跃的服务器`)
      return port
    } catch (e) {
      console.log(`尝试端口 ${port} 失败: ${e.message}`)
      // 继续尝试下一个端口
    }
  }
  
  // 如果没有找到活跃服务器，返回默认端口
  console.log('未找到活跃服务器，使用默认端口8089')
  return 8089
}

module.exports = defineConfig({
  transpileDependencies: true,
  chainWebpack: config => {
    config.plugin('define').tap(args => {
      // 添加Vue特性标志
      args[0].__VUE_PROD_HYDRATION_MISMATCH_DETAILS__ = JSON.stringify(false);
      args[0].__VUE_PROD_DEVTOOLS_GLOBAL_HOOK__ = JSON.stringify(false);
      return args;
    });
  },
  devServer: {
    // 允许系统动态分配端口，不再硬编码端口号
    port: process.env.PORT || 8080,
    proxy: {
      '/api/local': {
        target: 'http://localhost:8091',
        changeOrigin: true
      }
    },
    // 简化路由配置，移除重复的API路由
    setupMiddlewares: (middlewares, devServer) => {
      if (!devServer) {
        throw new Error('webpack-dev-server is not defined');
      }
      
      // 映射 userdata 目录为静态资源
      devServer.app.use(
        '/userdata',
        express.static(path.resolve(__dirname, 'userdata'))
      );
      
      // 添加调试信息路由
      devServer.app.get('/dev-info', (req, res) => {
        res.json({
          mode: 'development',
          proxyTarget: 'http://localhost:8089',
          timestamp: new Date().toISOString()
        });
      });
      
      return middlewares;
    },
    // 添加客户端配置，支持在任何端口下访问
    client: {
      webSocketURL: 'auto://0.0.0.0:0/ws'
    }
  },
  pluginOptions: {
    electronBuilder: {
      nodeIntegration: false,
      preload: 'src/preload.js',
      // 配置主进程入口
      mainProcessFile: 'src/background.js',
      // 如果你需要将资源绑定到应用程序
      builderOptions: {
        extraResources: ['./public/**']
      }
    }
  }
})
