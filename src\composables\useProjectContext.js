import { ref, provide, inject, readonly } from 'vue';

// 定义注入键名，避免命名冲突
const PROJECT_CONTEXT_KEY = 'projectContext';

/**
 * 提供项目上下文信息的组合式函数
 * 在主应用组件中使用此函数的 Provider 部分
 *
 * @returns {Object} 返回项目上下文的提供者和相关方法
 */
export function provideProjectContext() {
  // 创建响应式状态
  const projectTitle = ref('');
  const chapterTitle = ref('');
  const currentStep = ref('');
  const projectData = ref(null);

  // 更新方法
  const setProjectTitle = (title) => {
    console.log('[ProjectContext] 设置项目标题:', title);
    projectTitle.value = title;
  };

  const setChapterTitle = (title) => {
    console.log('[ProjectContext] 设置章节标题:', title);
    chapterTitle.value = title;
  };

  const setCurrentStep = (step) => {
    console.log('[ProjectContext] 设置当前步骤:', step);
    currentStep.value = step;
  };

  const setProjectData = (data) => {
    console.log('[ProjectContext] 设置项目数据:', data);
    projectData.value = data;
  };

  // 设置项目和章节信息
  const setProjectContext = (context) => {
    console.log('[ProjectContext] 设置项目上下文:', context);
    if (context.projectTitle !== undefined) {
      projectTitle.value = context.projectTitle;
    }
    if (context.chapterTitle !== undefined) {
      chapterTitle.value = context.chapterTitle;
    }
    if (context.currentStep !== undefined) {
      currentStep.value = context.currentStep;
    }
    if (context.projectData !== undefined) {
      projectData.value = context.projectData;
    }
  };

  // 提供给应用的其他部分
  provide(PROJECT_CONTEXT_KEY, {
    // 只读状态
    projectTitle: readonly(projectTitle),
    chapterTitle: readonly(chapterTitle),
    currentStep: readonly(currentStep),
    projectData: readonly(projectData),
    // 更新方法
    setProjectTitle,
    setChapterTitle,
    setCurrentStep,
    setProjectData,
    setProjectContext
  });

  // 返回提供的上下文和方法，以便在提供组件中使用
  return {
    projectTitle,
    chapterTitle,
    currentStep,
    projectData,
    setProjectTitle,
    setChapterTitle,
    setCurrentStep,
    setProjectData,
    setProjectContext
  };
}

/**
 * 注入项目上下文信息的组合式函数
 * 在需要访问项目上下文的组件中使用
 *
 * @returns {Object} 返回项目上下文的状态和方法
 */
export function useProjectContext() {
  const context = inject(PROJECT_CONTEXT_KEY, null);

  if (!context) {
    console.error('[ProjectContext] 项目上下文未提供。请确保在应用根级组件中使用 provideProjectContext()');

    // 返回默认值，避免应用崩溃
    return {
      projectTitle: ref(''),
      chapterTitle: ref(''),
      currentStep: ref(''),
      projectData: ref(null),
      setProjectTitle: () => console.warn('[ProjectContext] 未提供上下文，setProjectTitle 不可用'),
      setChapterTitle: () => console.warn('[ProjectContext] 未提供上下文，setChapterTitle 不可用'),
      setCurrentStep: () => console.warn('[ProjectContext] 未提供上下文，setCurrentStep 不可用'),
      setProjectData: () => console.warn('[ProjectContext] 未提供上下文，setProjectData 不可用'),
      setProjectContext: () => console.warn('[ProjectContext] 未提供上下文，setProjectContext 不可用')
    };
  }

  return context;
}