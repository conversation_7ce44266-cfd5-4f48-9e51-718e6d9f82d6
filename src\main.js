import { createApp } from 'vue'
import App from './App.vue'
import './assets/styles/button-hover.css'
import logger from './utils/logger'
import router from './router'

// 全局 fetch 日志挂件
const originalFetch = window.fetch;
window.fetch = async (...args) => {
  logger.info('[FETCH 请求]', ...args);
  try {
    const response = await originalFetch(...args);
    const cloned = response.clone();
    cloned.text().then(data => {
      logger.info('[FETCH 响应]', response.status, response.url, data);
    });
    return response;
  } catch (e) {
    logger.error('[FETCH 错误]', e);
    throw e;
  }
};

const app = createApp(App);
app.config.globalProperties.$logger = logger;
app.use(router);

app.mount('#app')
