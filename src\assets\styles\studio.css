.studio-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #121212;
  color: #e0e0e0;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

/* 主要内容区域样式 */
.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 中间面板样式 */
.center-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #1a1a1a;
  border-right: 1px solid #333;
}

.panel-header {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  background-color: #222;
  border-bottom: 1px solid #333;
}

.selected {
  font-size: 0.85rem;
  color: #aaa;
  margin-right: 1rem;
}

.section-title {
  font-size: 0.9rem;
  font-weight: bold;
}

.script-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0.5rem;
  position: relative;
}

.scene-label {
  background-color: #333;
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
  font-size: 0.85rem;
  margin-bottom: 0.5rem;
  display: inline-block;
}

textarea {
  flex: 1;
  background-color: #222;
  border: 1px solid #444;
  border-radius: 4px;
  color: #e0e0e0;
  padding: 0.5rem;
  resize: none;
  margin-bottom: 0.5rem;
}

.counter {
  position: absolute;
  bottom: 40px;
  left: 10px;
  font-size: 0.8rem;
  color: #aaa;
}

.editor-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.editor-button {
  background-color: #333;
  border: none;
  color: #e0e0e0;
  padding: 0.3rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
}

.page-navigation {
  display: flex;
  gap: 0.2rem;
}

.page-navigation button {
  background-color: #333;
  border: none;
  color: #e0e0e0;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  cursor: pointer;
}

.image-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 40px;
  grid-template-rows: auto 1fr 1fr;
  gap: 4px;
  flex: 1;
  min-height: 180px;
  position: relative;
}

.image-top-bar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 5px;
  height: 30px;
}

.top-bar-buttons {
  display: flex;
  gap: 5px;
}

.top-button {
  background-color: #333;
  border: none;
  color: white;
  padding: 3px 8px;
  border-radius: 3px;
  font-size: 12px;
  cursor: pointer;
}

.image-layout {
  display: flex;
  gap: 4px;
  width: 100%;
  min-height: 180px;
}

.main-image {
  flex: 2;
  background-color: #000;
  border-radius: 2px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.thumbnails-area {
  flex: 1;
  display: flex;
  gap: 4px;
}

.thumbnails-scroll-container {
  flex: 1;
  overflow-y: auto;
  max-height: 100%;
  background-color: #1a1a1a;
  border-radius: 2px;
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #444 #222;
}

.thumbnails-scroll-container::-webkit-scrollbar {
  width: 8px;
}

.thumbnails-scroll-container::-webkit-scrollbar-track {
  background: #222;
  border-radius: 4px;
}

.thumbnails-scroll-container::-webkit-scrollbar-thumb {
  background-color: #444;
  border-radius: 4px;
}

.thumbnails-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-auto-rows: minmax(80px, 1fr);
  gap: 4px;
  padding: 4px;
  min-height: 200px;
}

.image-item {
  background-color: #000;
  border-radius: 2px;
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 80px;
}

.sequence-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  background-color: #ff3d7f;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

/* 右侧面板样式 */
.right-panel {
  width: 25%;
  display: flex;
  flex-direction: column;
  background-color: #1a1a1a;
}

.tabs-header {
  display: flex;
  background-color: #222;
  border-bottom: 1px solid #333;
}

.tab {
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
  cursor: pointer;
}

.tab.active {
  background-color: #333;
  color: #fff;
}

.preview-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1rem;
}

.preview-area {
  flex: 1;
  background-color: #222;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.preview-controls {
  display: flex;
  justify-content: space-between;
}

.control-button {
  background-color: #333;
  border: none;
  color: #e0e0e0;
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
}

.multi-person-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.person-count {
  display: flex;
  align-items: center;
  gap: 0.2rem;
  font-size: 0.85rem;
}

.save-button {
  background-color: #2196f3;
  border: none;
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
}

/* 选择状态栏样式 */
.selection-status-bar {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background-color: #1e1e2c;
  border-bottom: 1px solid #333355;
}

.selection-info {
  font-size: 0.9rem;
  color: #aaa;
}

/* 完全重构Excel风格网格布局 */
.excel-grid-layout {
  display: table;
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  background-color: #1a1a1a;
  table-layout: fixed;
}

.grid-row {
  display: table-row;
}

.grid-cell {
  display: table-cell;
  text-align: center;
  vertical-align: middle;
  border-right: 1px solid #333;
  border-bottom: 1px solid #333;
  color: #e0e0e0;
  box-sizing: border-box;
  font-size: 0.9rem;
  padding: 0;
  overflow: hidden;
}

.header-row {
  background-color: #1e1e1e;
  height: 40px;
  font-weight: bold;
}

.header-row .grid-cell {
  border-bottom: 2px solid #444;
  padding: 8px;
}

.data-row {
  height: 250px;
}

.data-row .grid-cell {
  height: 250px;
  vertical-align: top;
  background-color: #252525;
}

/* 为每个列设置精确的宽度 */
.select-cell-header, .select-cell {
  width: 5%;
}

.index-header, .index-cell {
  width: 5%;
}

.description-cell, .description-header {
  width: 20%;
}

.tags-header, .tags-cell {
  width: 15%;
}

.keyword-header, .keyword-cell {
  width: 15%;
}

.image-header, .image-cell {
  width: 12%;
}

.optional-image-header, .optional-image-cell {
  width: 15%;
}

.operation-header, .operation-cell {
  width: 10%;
  border-right: none;
}

/* 内部内容容器 */
.cell-content {
  width: 100%;
  height: 100%;
  padding: 5px;
  box-sizing: border-box;
}

.select-header-btn {
  background-color: #2a2a2a;
  color: white;
  border: none;
  border-radius: 3px;
  padding: 4px 8px;
  font-size: 0.9rem;
  cursor: pointer;
  margin: 0 auto;
  text-align: center;
  display: block;
  width: 80%;
}

.select-header-btn:hover {
  background-color: #3a3a3a;
}

.image-header-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 0 2px;
}

.image-title {
  font-size: 14px;
  font-weight: bold;
  color: #e0e0e0;
}

.image-controls {
  display: flex;
  gap: 8px;
}

.control-icon-button {
  background-color: #333;
  border: none;
  color: white;
  padding: 3px 8px;
  border-radius: 3px;
  font-size: 12px;
  cursor: pointer;
}

.lock-button {
  background-color: #2a2a2a;
}

.clear-button {
  background-color: #444;
}

/* 画面关键词样式 */
.keyword-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #1e1e1e;
  border-radius: 2px;
  overflow: hidden;
}

.keyword-textarea {
  flex: 1;
  background-color: #111;
  border: none;
  color: #e0e0e0;
  font-size: 14px;
  padding: 10px;
  resize: none;
  width: 100%;
  outline: none;
  min-height: 180px;
}

/* 选中列的选择控件和按钮样式 */
.select-controls {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 5px;
  padding: 10px 2px;
}

.select-checkbox {
  width: 18px;
  height: 18px;
  min-width: 18px;
  border: 1px solid #00BCD4;
  border-radius: 3px;
  background-color: transparent;
  cursor: pointer;
  margin-top: 10px;
}

.button-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.merge-button, .split-button {
  background-color: #333;
  border: none;
  color: white;
  padding: 3px 5px;
  font-size: 0.7rem;
  border-radius: 3px;
  cursor: pointer;
  white-space: nowrap;
}

.merge-button:hover, .split-button:hover {
  background-color: #444;
}

/* 原文描述样式 */
.description-box {
  width: 100%;
  height: 100%;
  background-color: #111;
  border: none;
  border-radius: 3px;
  overflow-y: auto;
  position: relative;
}

.readonly-text {
  color: #e0e0e0;
  font-size: 14px;
  padding: 10px;
  line-height: 1.5;
  white-space: pre-wrap;
  user-select: text;
  cursor: text;
  height: 100%;
  overflow-y: auto;
}

/* 标签区域样式 */
.tags-container {
  width: 100%;
  height: 100%;
  background-color: #1e1e1e;
  border-radius: 2px;
  padding: 10px;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  gap: 8px;
  overflow-y: auto;
}

.vertical-manage-btn {
  width: 40px;
  background-color: #2a2a2a;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.vertical-text {
  writing-mode: vertical-rl;
  text-orientation: upright;
  color: white;
  font-size: 14px;
  letter-spacing: 2px;
  user-select: none;
}

/* 主图区域调整 */
.image-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #1e1e1e;
  border-radius: 2px;
  position: relative;
}

.main-image {
  flex: 1;
  background-color: #000;
  border-radius: 2px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

/* 可选图片区域样式 */
.optional-image-container {
  width: 100%;
  height: 100%;
  background-color: #1e1e1e;
  border-radius: 2px;
  display: flex;
  gap: 4px;
}

/* 操作列样式 */
.operation-container {
  width: 100%;
  height: 100%;
  background-color: #1e1e1e;
  border-radius: 2px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 8px;
}

.operation-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.operation-button {
  width: 100%;
  background-color: #333;
  border: none;
  color: white;
  padding: 6px 0;
  border-radius: 3px;
  font-size: 13px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.operation-button:hover {
  background-color: #444;
}

.operation-button.highlight {
  background-color: #444;
  color: white;
  font-weight: bold;
  border: 1px solid #555;
}