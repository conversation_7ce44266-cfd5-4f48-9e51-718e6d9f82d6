/**
 * ComfyUI通信模块
 * 负责与本地ComfyUI进行通信，发送提示词生成图片
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { ipc<PERSON><PERSON>er } = require('electron');

// ComfyUI默认地址
const COMFY_UI_API = 'http://localhost:8188/api';

/**
 * 检查ComfyUI是否在线
 * @returns {Promise<boolean>} ComfyUI是否可访问
 */
async function checkComfyUIStatus() {
  try {
    const response = await axios.get(`${COMFY_UI_API}/system_stats`, { timeout: 5000 });
    return response.status === 200;
  } catch (error) {
    console.error('ComfyUI连接错误:', error.message);
    return false;
  }
}

/**
 * 获取ComfyUI中可用的模型列表
 * @returns {Promise<Array>} 模型列表
 */
async function getAvailableModels() {
  try {
    const response = await axios.get(`${COMFY_UI_API}/object_info`);
    if (response.data && response.data.CheckpointLoaderSimple) {
      return response.data.CheckpointLoaderSimple.input.required.ckpt_name[0];
    }
    return [];
  } catch (error) {
    console.error('获取模型列表错误:', error);
    return [];
  }
}

/**
 * 构建用于生成图片的Workflow
 * @param {string} prompt 提示词
 * @param {string} negativePrompt 负面提示词
 * @param {Object} options 生成选项
 * @returns {Object} ComfyUI工作流对象
 */
function buildWorkflow(prompt, negativePrompt = "bad quality, blurry", options = {}) {
  const {
    model = "animerge_v31.safetensors",
    seed = Math.floor(Math.random() * 2147483647),
    width = 768,
    height = 512,
    steps = 20,
    cfg = 7
  } = options;

  return {
    "3": {
      "inputs": {
        "text": prompt,
        "clip": ["4", 0]
      },
      "class_type": "CLIPTextEncode"
    },
    "4": {
      "inputs": {
        "ckpt_name": model
      },
      "class_type": "CheckpointLoaderSimple"
    },
    "5": {
      "inputs": {
        "text": negativePrompt,
        "clip": ["4", 0]
      },
      "class_type": "CLIPTextEncode"
    },
    "8": {
      "inputs": {
        "samples": ["9", 0],
        "vae": ["4", 2]
      },
      "class_type": "VAEDecode"
    },
    "9": {
      "inputs": {
        "seed": seed,
        "steps": steps,
        "cfg": cfg,
        "sampler_name": "euler_ancestral",
        "scheduler": "normal",
        "denoise": 1,
        "model": ["4", 0],
        "positive": ["3", 0],
        "negative": ["5", 0],
        "latent_image": ["10", 0]
      },
      "class_type": "KSampler"
    },
    "10": {
      "inputs": {
        "width": width,
        "height": height,
        "batch_size": 1
      },
      "class_type": "EmptyLatentImage"
    },
    "11": {
      "inputs": {
        "filename_prefix": "comic_frame_",
        "images": ["8", 0]
      },
      "class_type": "SaveImage"
    }
  };
}

/**
 * 向ComfyUI发送请求生成图片
 * @param {string} prompt 提示词
 * @param {Function} progressCallback 进度回调函数
 * @param {Object} options 生成选项
 * @returns {Promise<string>} 生成的图片路径
 */
async function generateImage(prompt, progressCallback = null, options = {}) {
  try {
    // 检查ComfyUI状态
    const isComfyUIOnline = await checkComfyUIStatus();
    if (!isComfyUIOnline) {
      throw new Error('ComfyUI未运行或无法访问');
    }

    // 构建工作流
    const workflow = buildWorkflow(prompt, options.negativePrompt, options);
    
    // 发送队列请求
    const promptResponse = await axios.post(`${COMFY_UI_API}/prompt`, {
      prompt: workflow
    });
    
    const promptId = promptResponse.data.prompt_id;
    
    // 等待生成完成
    const imagePath = await waitForImageGeneration(promptId, progressCallback);
    
    // 如果需要保存图片到本地，可以在这里添加代码
    if (options.saveToDisk) {
      try {
        const imageResponse = await axios.get(imagePath, { responseType: 'arraybuffer' });
        const imageName = `frame_${Date.now()}.png`;
        const savePath = path.join(options.saveDir || '.', imageName);
        
        await fs.promises.writeFile(savePath, Buffer.from(imageResponse.data));
        return savePath;
      } catch (error) {
        console.error('保存图片错误:', error);
        return imagePath;
      }
    }
    
    return imagePath;
  } catch (error) {
    console.error('生成图片错误:', error);
    throw error;
  }
}

/**
 * 等待图像生成完成
 * @param {string} promptId 提示ID
 * @param {Function} progressCallback 进度回调函数
 * @returns {Promise<string>} 生成的图片路径
 */
async function waitForImageGeneration(promptId, progressCallback) {
  return new Promise((resolve, reject) => {
    let lastProgressValue = 0;
    
    // 使用轮询检查生成状态
    const checkInterval = setInterval(async () => {
      try {
        const response = await axios.get(`${COMFY_UI_API}/history/${promptId}`);
        
        if (response.data && response.data[promptId]) {
          const historyData = response.data[promptId];
          
          // 如果有进度信息，调用进度回调
          if (historyData.progress && progressCallback) {
            const currentProgress = historyData.progress.value;
            
            // 只有当进度发生变化时才调用回调
            if (currentProgress !== lastProgressValue) {
              progressCallback(currentProgress, historyData.progress.max);
              lastProgressValue = currentProgress;
            }
          }
          
          // 检查是否生成完成
          if (historyData.status && historyData.status.completed) {
            clearInterval(checkInterval);
            
            // 如果有输出节点11（SaveImage），则获取图片路径
            if (historyData.outputs && historyData.outputs['11']) {
              const images = historyData.outputs['11'].images;
              if (images && images.length > 0) {
                resolve(`${COMFY_UI_API}/view?filename=${images[0].filename}&subfolder=${images[0].subfolder || ''}`);
              } else {
                reject(new Error('生成完成，但没有找到图片'));
              }
            } else {
              reject(new Error('生成完成，但没有找到输出节点'));
            }
          }
        }
      } catch (error) {
        console.error('检查生成状态错误:', error);
      }
    }, 1000); // 每秒检查一次
    
    // 120秒超时
    setTimeout(() => {
      clearInterval(checkInterval);
      reject(new Error('生成图片超时'));
    }, 120000);
  });
}

/**
 * 取消当前正在进行的生成任务
 */
async function cancelGeneration() {
  try {
    await axios.post(`${COMFY_UI_API}/queue`, {
      clear: true
    });
    console.log('已取消所有生成任务');
    return true;
  } catch (error) {
    console.error('取消生成任务错误:', error);
    return false;
  }
}

module.exports = {
  checkComfyUIStatus,
  getAvailableModels,
  generateImage,
  cancelGeneration
}; 